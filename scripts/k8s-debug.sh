#!/bin/bash

# Kubernetes Deployment Debugging Script
# Usage: ./scripts/k8s-debug.sh [service-name]

set -e

SERVICE=${1:-"all"}
NAMESPACE="abraapi"

echo "🔍 Kubernetes Deployment Debugging Script"
echo "=========================================="

# Function to check a specific service
debug_service() {
    local service_name=$1
    echo ""
    echo "🔍 Debugging: $service_name"
    echo "----------------------------------------"
    
    # Check deployment status
    echo "📊 Deployment Status:"
    kubectl get deployment $service_name -n $NAMESPACE -o wide || echo "❌ Deployment not found"
    
    # Check pods
    echo ""
    echo "🏃 Pod Status:"
    kubectl get pods -l app=$service_name -n $NAMESPACE -o wide || echo "❌ No pods found"
    
    # Check recent events
    echo ""
    echo "📋 Recent Events:"
    kubectl get events -n $NAMESPACE --field-selector involvedObject.name=$service_name --sort-by='.lastTimestamp' | tail -10 || echo "❌ No events found"
    
    # Get pod logs if pods exist
    echo ""
    echo "📝 Pod Logs (last 20 lines):"
    local pods=$(kubectl get pods -l app=$service_name -n $NAMESPACE -o jsonpath='{.items[*].metadata.name}' 2>/dev/null)
    if [ -n "$pods" ]; then
        for pod in $pods; do
            echo "--- Logs for $pod ---"
            kubectl logs $pod -n $NAMESPACE --tail=20 || echo "❌ Could not get logs for $pod"
            echo ""
        done
    else
        echo "❌ No pods found for $service_name"
    fi
    
    # Check secrets
    echo ""
    echo "🔐 Secret Status:"
    kubectl get secret ${service_name}-secret -n $NAMESPACE -o yaml | grep -A 10 "data:" || echo "❌ Secret not found"
    
    # Check configmap
    echo ""
    echo "⚙️ ConfigMap Status:"
    kubectl get configmap ${service_name}-config -n $NAMESPACE -o yaml | grep -A 10 "data:" || echo "❌ ConfigMap not found"
}

# Function to check overall cluster health
check_cluster_health() {
    echo ""
    echo "🏥 Cluster Health Check"
    echo "----------------------------------------"
    
    echo "📊 Node Status:"
    kubectl get nodes -o wide
    
    echo ""
    echo "📊 Namespace Status:"
    kubectl get namespaces
    
    echo ""
    echo "📊 All Pods in $NAMESPACE:"
    kubectl get pods -n $NAMESPACE -o wide
    
    echo ""
    echo "📊 All Services in $NAMESPACE:"
    kubectl get services -n $NAMESPACE -o wide
    
    echo ""
    echo "📊 All Ingresses in $NAMESPACE:"
    kubectl get ingress -n $NAMESPACE -o wide
    
    echo ""
    echo "📊 Resource Usage:"
    kubectl top nodes 2>/dev/null || echo "❌ Metrics server not available"
    kubectl top pods -n $NAMESPACE 2>/dev/null || echo "❌ Pod metrics not available"
}

# Main execution
if [ "$SERVICE" = "all" ]; then
    check_cluster_health
    echo ""
    echo "🔍 Debugging All Services"
    echo "=========================================="
    
    for service in auth-service assistant-service marketdata-service thread-service; do
        debug_service $service
    done
else
    debug_service $SERVICE
fi

echo ""
echo "✅ Debugging complete!"
echo ""
echo "💡 Common Issues & Solutions:"
echo "----------------------------------------"
echo "1. CrashLoopBackOff: Check pod logs for application errors"
echo "2. ImagePullBackOff: Verify image exists and pull secrets are correct"
echo "3. Pending: Check resource requests vs available cluster resources"
echo "4. Health check failures: Verify /health endpoint is responding"
echo "5. Secret/ConfigMap issues: Ensure all required keys are present"
echo ""
echo "🔧 Useful Commands:"
echo "kubectl describe pod <pod-name> -n $NAMESPACE"
echo "kubectl logs <pod-name> -n $NAMESPACE -f"
echo "kubectl exec -it <pod-name> -n $NAMESPACE -- /bin/sh"
echo "kubectl port-forward <pod-name> 8080:8080 -n $NAMESPACE"
