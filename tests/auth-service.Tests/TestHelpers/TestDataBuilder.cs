using System.Security.Claims;
using AuthService.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AuthService.Tests.TestHelpers;

public static class TestDataBuilder
{
    // Removed old UserProfile method - now using SupabaseUser instead

    // Removed old JSON method - now using SupabaseUser objects directly

    public static SupabaseUser CreateSupabaseUser(
        string userId = "test-user-id",
        string email = "<EMAIL>",
        string? fullName = "Test User",
        string? username = "testuser",
        string? brokerId = null,
        bool verified = true)
    {
        return new SupabaseUser
        {
            Id = userId,
            Email = email,
            EmailConfirmedAt = verified ? DateTime.UtcNow : null,
            CreatedAt = DateTime.UtcNow,
            UserMetadata = new Dictionary<string, object>
            {
                ["full_name"] = fullName ?? "",
                ["username"] = username ?? "",
                ["broker_id"] = brokerId ?? ""
            }
        };
    }

    public static ClaimsPrincipal CreateClaimsPrincipal(
        string userId = "test-user-id",
        string email = "<EMAIL>",
        string name = "Test User")
    {
        var claims = new List<Claim>
        {
            new("sub", userId),
            new("email", email),
            new("name", name)
        };

        var identity = new ClaimsIdentity(claims, "test");
        return new ClaimsPrincipal(identity);
    }

    public static ClaimsPrincipal CreateClaimsPrincipalWithoutSub(
        string email = "<EMAIL>",
        string name = "Test User")
    {
        var claims = new List<Claim>
        {
            new("email", email),
            new("name", name)
        };

        var identity = new ClaimsIdentity(claims, "test");
        return new ClaimsPrincipal(identity);
    }

    public static ControllerContext CreateControllerContext(ClaimsPrincipal user)
    {
        return new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = user }
        };
    }

    public static ControllerContext CreateControllerContextWithUser(
        string userId = "test-user-id",
        string email = "<EMAIL>",
        string name = "Test User")
    {
        var user = CreateClaimsPrincipal(userId, email, name);
        return CreateControllerContext(user);
    }

    public static ControllerContext CreateControllerContextWithoutSub(
        string email = "<EMAIL>",
        string name = "Test User")
    {
        var user = CreateClaimsPrincipalWithoutSub(email, name);
        return CreateControllerContext(user);
    }
}
