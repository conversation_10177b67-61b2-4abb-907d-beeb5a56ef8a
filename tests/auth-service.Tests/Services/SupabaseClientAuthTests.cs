using System.Net;
using System.Text;
using System.Text.Json;
using AuthService.Services;
using AuthService.Models;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Moq;
using Moq.Protected;

namespace AuthService.Tests.Services;

public class SupabaseClientAuthTests
{
    private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
    private readonly HttpClient _httpClient;
    private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
    private readonly Mock<IConfiguration> _configurationMock;
    private readonly Mock<ICacheService> _cacheServiceMock;
    private readonly SupabaseClient _supabaseClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public SupabaseClientAuthTests()
    {
        _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_httpMessageHandlerMock.Object);
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();
        _configurationMock = new Mock<IConfiguration>();
        _cacheServiceMock = new Mock<ICacheService>();

        // Setup configuration
        _configurationMock.Setup(x => x["SUPABASE_URL"]).Returns("https://test.supabase.co");
        _configurationMock.Setup(x => x["SUPABASE_SERVICE_ROLE_KEY"]).Returns("test-service-role-key");
        _configurationMock.Setup(x => x["SUPABASE_ANON_KEY"]).Returns("test-anon-key");

        // Setup HttpClientFactory to return our mocked HttpClient
        _httpClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(_httpClient);

        _supabaseClient = new SupabaseClient(_httpClient, _httpClientFactoryMock.Object, _cacheServiceMock.Object, _configurationMock.Object);
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
        };
    }

    [Fact]
    public async Task SignupAsync_WithValidRequest_ReturnsAuthResponse()
    {
        // Arrange
        var request = new SignupRequest
        {
            Email = "<EMAIL>",
            Password = "password123"
        };

        var expectedResponse = new AuthResponse
        {
            AccessToken = "access_token",
            RefreshToken = "refresh_token",
            TokenType = "bearer",
            ExpiresIn = 3600
        };

        var responseJson = JsonSerializer.Serialize(expectedResponse, _jsonOptions);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _supabaseClient.SignupAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.AccessToken.Should().Be("access_token");
        result.RefreshToken.Should().Be("refresh_token");
        result.TokenType.Should().Be("bearer");
        result.ExpiresIn.Should().Be(3600);
    }

    [Fact]
    public async Task LoginAsync_WithValidRequest_ReturnsAuthResponse()
    {
        // Arrange
        var request = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "password123"
        };

        var expectedResponse = new AuthResponse
        {
            AccessToken = "access_token",
            RefreshToken = "refresh_token",
            TokenType = "bearer",
            ExpiresIn = 3600
        };

        var responseJson = JsonSerializer.Serialize(expectedResponse, _jsonOptions);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _supabaseClient.LoginAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.AccessToken.Should().Be("access_token");
        result.RefreshToken.Should().Be("refresh_token");
    }

    [Fact]
    public async Task RefreshTokenAsync_WithValidRequest_ReturnsAuthResponse()
    {
        // Arrange
        var request = new TokenRefreshRequest
        {
            RefreshToken = "refresh_token"
        };

        var expectedResponse = new AuthResponse
        {
            AccessToken = "new_access_token",
            RefreshToken = "new_refresh_token",
            TokenType = "bearer",
            ExpiresIn = 3600
        };

        var responseJson = JsonSerializer.Serialize(expectedResponse, _jsonOptions);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _supabaseClient.RefreshTokenAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.AccessToken.Should().Be("new_access_token");
        result.RefreshToken.Should().Be("new_refresh_token");
    }

    [Fact]
    public async Task RecoverPasswordAsync_WithValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new PasswordRecoveryRequest
        {
            Email = "<EMAIL>"
        };

        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent("{}", Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _supabaseClient.RecoverPasswordAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Message.Should().Be("Password recovery email sent");
    }

    [Fact]
    public async Task SendOtpAsync_WithValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new OtpRequest
        {
            Email = "<EMAIL>",
            CreateUser = true
        };

        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent("{}", Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _supabaseClient.SendOtpAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Message.Should().Be("OTP sent successfully");
    }

    [Fact]
    public async Task SignupAsync_WithHttpError_ThrowsHttpRequestException()
    {
        // Arrange
        var request = new SignupRequest
        {
            Email = "<EMAIL>",
            Password = "password123"
        };

        var errorResponse = new AuthErrorResponse
        {
            Error = "invalid_credentials",
            ErrorDescription = "Invalid email or password",
            Message = "Invalid email or password"
        };

        var responseJson = JsonSerializer.Serialize(errorResponse, _jsonOptions);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(() => _supabaseClient.SignupAsync(request));
        exception.Message.Should().Contain("Signup failed");
    }

    [Fact]
    public async Task GetOAuthUrlAsync_WithValidRequest_ReturnsOAuthResponse()
    {
        // Arrange
        var request = new OAuthRequest
        {
            Provider = "google",
            RedirectTo = "https://example.com/callback"
        };

        // Act
        var result = await _supabaseClient.GetOAuthUrlAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Provider.Should().Be("google");
        result.Url.Should().Contain("provider=google");
        result.Url.Should().Contain("redirect_to=https%3A%2F%2Fexample.com%2Fcallback");
    }
}
