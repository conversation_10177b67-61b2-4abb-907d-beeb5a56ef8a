using AuthService.Models;
using AuthService.Controllers;
using AuthService.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace AuthService.Tests.Controllers;

public class AuthControllerGoogleSignInTests
{
    private readonly Mock<ISupabaseClient> _supabaseClientMock;
    private readonly AuthController _controller;

    public AuthControllerGoogleSignInTests()
    {
        _supabaseClientMock = new Mock<ISupabaseClient>();
        _controller = new AuthController(_supabaseClientMock.Object);
    }

    [Fact]
    public async Task GetGoogleSignInUrl_WithoutRedirectTo_ReturnsOkWithOAuthResponse()
    {
        // Arrange
        var expectedResponse = new OAuthResponse
        {
            Url = "https://test.supabase.co/auth/v1/authorize?provider=google",
            Provider = "google"
        };

        _supabaseClientMock
            .Setup(x => x.GetGoogleSignInUrlAsync(null))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.GetGoogleSignInUrl();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var response = okResult.Value as OAuthResponse;
        response.Should().NotBeNull();
        response!.Provider.Should().Be("google");
        response.Url.Should().Contain("provider=google");
    }

    [Fact]
    public async Task GetGoogleSignInUrl_WithRedirectTo_ReturnsOkWithOAuthResponse()
    {
        // Arrange
        var request = new GoogleSignInRequest
        {
            RedirectTo = "https://example.com/callback"
        };

        var expectedResponse = new OAuthResponse
        {
            Url = "https://test.supabase.co/auth/v1/authorize?provider=google&redirect_to=https%3A%2F%2Fexample.com%2Fcallback",
            Provider = "google"
        };

        _supabaseClientMock
            .Setup(x => x.GetGoogleSignInUrlAsync(request.RedirectTo))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.GetGoogleSignInUrl(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var response = okResult.Value as OAuthResponse;
        response.Should().NotBeNull();
        response!.Provider.Should().Be("google");
        response.Url.Should().Contain("redirect_to=");
    }

    [Fact]
    public async Task GetGoogleSignInUrl_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest()
    {
        // Arrange
        _supabaseClientMock
            .Setup(x => x.GetGoogleSignInUrlAsync(It.IsAny<string?>()))
            .ThrowsAsync(new HttpRequestException("OAuth URL generation failed"));

        // Act
        var result = await _controller.GetGoogleSignInUrl();

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorResponse = badRequestResult.Value;
        errorResponse.Should().NotBeNull();
    }

    [Fact]
    public async Task GoogleCallback_WithValidCode_ReturnsOkWithAuthResponse()
    {
        // Arrange
        var request = new GoogleCallbackRequest
        {
            Code = "valid-auth-code"
        };

        var expectedResponse = new AuthResponse
        {
            AccessToken = "test-access-token",
            RefreshToken = "test-refresh-token",
            TokenType = "bearer",
            ExpiresIn = 3600,
            User = new SupabaseUser
            {
                Id = "test-user-id",
                Email = "<EMAIL>"
            }
        };

        _supabaseClientMock
            .Setup(x => x.SignInWithGoogleAsync(request.Code))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.GoogleCallback(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var response = okResult.Value as AuthResponse;
        response.Should().NotBeNull();
        response!.AccessToken.Should().Be("test-access-token");
        response.User.Should().NotBeNull();
        response.User!.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task GoogleCallback_WithEmptyCode_ReturnsBadRequest()
    {
        // Arrange
        var request = new GoogleCallbackRequest
        {
            Code = ""
        };

        // Act
        var result = await _controller.GoogleCallback(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorMessage = badRequestResult.Value?.ToString();
        errorMessage.Should().Contain("Authorization code is required");
    }

    [Fact]
    public async Task GoogleCallback_WithNullCode_ReturnsBadRequest()
    {
        // Arrange
        var request = new GoogleCallbackRequest
        {
            Code = null!
        };

        // Act
        var result = await _controller.GoogleCallback(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorMessage = badRequestResult.Value?.ToString();
        errorMessage.Should().Contain("Authorization code is required");
    }

    [Fact]
    public async Task GoogleCallback_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest()
    {
        // Arrange
        var request = new GoogleCallbackRequest
        {
            Code = "invalid-code"
        };

        _supabaseClientMock
            .Setup(x => x.SignInWithGoogleAsync(request.Code))
            .ThrowsAsync(new HttpRequestException("Invalid authorization code"));

        // Act
        var result = await _controller.GoogleCallback(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorResponse = badRequestResult.Value;
        errorResponse.Should().NotBeNull();
    }

    [Fact]
    public async Task GoogleCallback_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError()
    {
        // Arrange
        var request = new GoogleCallbackRequest
        {
            Code = "test-code"
        };

        _supabaseClientMock
            .Setup(x => x.SignInWithGoogleAsync(request.Code))
            .ThrowsAsync(new InvalidOperationException("Unexpected error"));

        // Act
        var result = await _controller.GoogleCallback(request);

        // Assert
        result.Should().BeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.Should().Be(500);
    }
}
