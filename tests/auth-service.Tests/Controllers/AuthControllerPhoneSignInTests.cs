using AuthService.Models;
using AuthService.Controllers;
using AuthService.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace AuthService.Tests.Controllers;

public class AuthControllerPhoneSignInTests
{
    private readonly Mock<ISupabaseClient> _supabaseClientMock;
    private readonly AuthController _controller;

    public AuthControllerPhoneSignInTests()
    {
        _supabaseClientMock = new Mock<ISupabaseClient>();
        _controller = new AuthController(_supabaseClientMock.Object);
    }

    [Fact]
    public async Task SendPhoneOtp_WithValidPhoneNumber_ReturnsOkWithSuccessResponse()
    {
        // Arrange
        var request = new PhoneOtpRequest
        {
            PhoneNumber = "+1234567890",
            CreateUser = true
        };

        var expectedResponse = new SuccessResponse
        {
            Message = "OTP sent successfully"
        };

        _supabaseClientMock
            .Setup(x => x.SendPhoneOtpAsync(request.PhoneNumber, request.CreateUser))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SendPhoneOtp(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var response = okResult.Value as SuccessResponse;
        response.Should().NotBeNull();
        response!.Message.Should().Be("OTP sent successfully");
    }

    [Fact]
    public async Task SendPhoneOtp_WithEmptyPhoneNumber_ReturnsBadRequest()
    {
        // Arrange
        var request = new PhoneOtpRequest
        {
            PhoneNumber = "",
            CreateUser = true
        };

        // Act
        var result = await _controller.SendPhoneOtp(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorMessage = badRequestResult.Value?.ToString();
        errorMessage.Should().Contain("Phone number is required");
    }

    [Fact]
    public async Task SendPhoneOtp_WithNullPhoneNumber_ReturnsBadRequest()
    {
        // Arrange
        var request = new PhoneOtpRequest
        {
            PhoneNumber = null!,
            CreateUser = true
        };

        // Act
        var result = await _controller.SendPhoneOtp(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorMessage = badRequestResult.Value?.ToString();
        errorMessage.Should().Contain("Phone number is required");
    }

    [Fact]
    public async Task SendPhoneOtp_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest()
    {
        // Arrange
        var request = new PhoneOtpRequest
        {
            PhoneNumber = "+1234567890",
            CreateUser = true
        };

        _supabaseClientMock
            .Setup(x => x.SendPhoneOtpAsync(request.PhoneNumber, request.CreateUser))
            .ThrowsAsync(new HttpRequestException("Invalid phone number format"));

        // Act
        var result = await _controller.SendPhoneOtp(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorResponse = badRequestResult.Value;
        errorResponse.Should().NotBeNull();
    }

    [Fact]
    public async Task SignInWithPhone_WithValidCredentials_ReturnsOkWithAuthResponse()
    {
        // Arrange
        var request = new PhoneSignInRequest
        {
            PhoneNumber = "+1234567890",
            OtpCode = "123456"
        };

        var expectedResponse = new AuthResponse
        {
            AccessToken = "test-access-token",
            RefreshToken = "test-refresh-token",
            TokenType = "bearer",
            ExpiresIn = 3600,
            User = new SupabaseUser
            {
                Id = "test-user-id",
                Phone = request.PhoneNumber
            }
        };

        _supabaseClientMock
            .Setup(x => x.SignInWithPhoneAsync(request.PhoneNumber, request.OtpCode))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SignInWithPhone(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var response = okResult.Value as AuthResponse;
        response.Should().NotBeNull();
        response!.AccessToken.Should().Be("test-access-token");
        response.User.Should().NotBeNull();
        response.User!.Phone.Should().Be(request.PhoneNumber);
    }

    [Fact]
    public async Task SignInWithPhone_WithEmptyPhoneNumber_ReturnsBadRequest()
    {
        // Arrange
        var request = new PhoneSignInRequest
        {
            PhoneNumber = "",
            OtpCode = "123456"
        };

        // Act
        var result = await _controller.SignInWithPhone(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorMessage = badRequestResult.Value?.ToString();
        errorMessage.Should().Contain("Phone number and OTP code are required");
    }

    [Fact]
    public async Task SignInWithPhone_WithEmptyOtpCode_ReturnsBadRequest()
    {
        // Arrange
        var request = new PhoneSignInRequest
        {
            PhoneNumber = "+1234567890",
            OtpCode = ""
        };

        // Act
        var result = await _controller.SignInWithPhone(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorMessage = badRequestResult.Value?.ToString();
        errorMessage.Should().Contain("Phone number and OTP code are required");
    }

    [Fact]
    public async Task SignInWithPhone_WithNullValues_ReturnsBadRequest()
    {
        // Arrange
        var request = new PhoneSignInRequest
        {
            PhoneNumber = null!,
            OtpCode = null!
        };

        // Act
        var result = await _controller.SignInWithPhone(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorMessage = badRequestResult.Value?.ToString();
        errorMessage.Should().Contain("Phone number and OTP code are required");
    }

    [Fact]
    public async Task SignInWithPhone_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest()
    {
        // Arrange
        var request = new PhoneSignInRequest
        {
            PhoneNumber = "+1234567890",
            OtpCode = "invalid"
        };

        _supabaseClientMock
            .Setup(x => x.SignInWithPhoneAsync(request.PhoneNumber, request.OtpCode))
            .ThrowsAsync(new HttpRequestException("Invalid OTP code"));

        // Act
        var result = await _controller.SignInWithPhone(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        var errorResponse = badRequestResult.Value;
        errorResponse.Should().NotBeNull();
    }

    [Fact]
    public async Task SignInWithPhone_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError()
    {
        // Arrange
        var request = new PhoneSignInRequest
        {
            PhoneNumber = "+1234567890",
            OtpCode = "123456"
        };

        _supabaseClientMock
            .Setup(x => x.SignInWithPhoneAsync(request.PhoneNumber, request.OtpCode))
            .ThrowsAsync(new InvalidOperationException("Unexpected error"));

        // Act
        var result = await _controller.SignInWithPhone(request);

        // Assert
        result.Should().BeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task SendPhoneOtp_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError()
    {
        // Arrange
        var request = new PhoneOtpRequest
        {
            PhoneNumber = "+1234567890",
            CreateUser = true
        };

        _supabaseClientMock
            .Setup(x => x.SendPhoneOtpAsync(request.PhoneNumber, request.CreateUser))
            .ThrowsAsync(new InvalidOperationException("Unexpected error"));

        // Act
        var result = await _controller.SendPhoneOtp(request);

        // Assert
        result.Should().BeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.Should().Be(500);
    }
}
