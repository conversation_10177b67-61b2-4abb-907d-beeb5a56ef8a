using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using Xunit;
using AssistantService.Controllers;
using AssistantService.Models;
using AssistantService.Services;

namespace AssistantService.Tests.Controllers;

public class ChatHistoryControllerTests
{
    private readonly Mock<IChatStorageService> _chatStorageMock;
    private readonly Mock<ILogger<ChatHistoryController>> _loggerMock;
    private readonly ChatHistoryController _controller;

    public ChatHistoryControllerTests()
    {
        _chatStorageMock = new Mock<IChatStorageService>();
        _loggerMock = new Mock<ILogger<ChatHistoryController>>();
        _controller = new ChatHistoryController(_chatStorageMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task GetChatHistory_ShouldReturnOk_WhenValidRequest()
    {
        // Arrange
        var request = new ChatHistoryRequest
        {
            Page = 1,
            PageSize = 10
        };

        var expectedResponse = new ChatHistoryResponse
        {
            Messages = new List<ChatMessageResponse>
            {
                new() { Id = Guid.NewGuid(), Message = "Test message", Role = "user" }
            },
            TotalCount = 1,
            Page = 1,
            PageSize = 10,
            TotalPages = 1
        };

        _chatStorageMock.Setup(x => x.GetChatHistoryAsync(It.IsAny<Guid>(), request))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.GetChatHistory(request);

        // Assert
        result.Should().BeOfType<ActionResult<ChatHistoryResponse>>();
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var response = okResult.Value.Should().BeOfType<ChatHistoryResponse>().Subject;
        response.Messages.Should().HaveCount(1);
        response.TotalCount.Should().Be(1);
    }

    [Fact]
    public async Task GetChatHistory_ShouldReturnInternalServerError_WhenExceptionThrown()
    {
        // Arrange
        var request = new ChatHistoryRequest();
        _chatStorageMock.Setup(x => x.GetChatHistoryAsync(It.IsAny<Guid>(), request))
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.GetChatHistory(request);

        // Assert
        var statusResult = result.Result.Should().BeOfType<ObjectResult>().Subject;
        statusResult.StatusCode.Should().Be(500);
        statusResult.Value.Should().Be("Failed to retrieve chat history");
    }

    [Fact]
    public async Task GetChatSessions_ShouldReturnOk_WhenValidRequest()
    {
        // Arrange
        var expectedSessions = new List<ChatSessionResponse>
        {
            new() { SessionId = "session-1", MessageCount = 5, LastMessage = "Hello" },
            new() { SessionId = "session-2", MessageCount = 3, LastMessage = "Goodbye" }
        };

        _chatStorageMock.Setup(x => x.GetChatSessionsAsync(It.IsAny<Guid>(), 1, 20))
            .ReturnsAsync(expectedSessions);

        // Act
        var result = await _controller.GetChatSessions();

        // Assert
        result.Should().BeOfType<ActionResult<List<ChatSessionResponse>>>();
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var sessions = okResult.Value.Should().BeOfType<List<ChatSessionResponse>>().Subject;
        sessions.Should().HaveCount(2);
    }

    [Fact]
    public async Task GetChatSessions_ShouldValidatePaginationParameters()
    {
        // Arrange
        _chatStorageMock.Setup(x => x.GetChatSessionsAsync(It.IsAny<Guid>(), It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync(new List<ChatSessionResponse>());

        // Act
        await _controller.GetChatSessions(page: -1, pageSize: 200);

        // Assert
        _chatStorageMock.Verify(x => x.GetChatSessionsAsync(It.IsAny<Guid>(), 1, 20), Times.Once);
    }

    [Fact]
    public async Task GetSessionMessages_ShouldReturnOk_WhenValidSessionId()
    {
        // Arrange
        var sessionId = "session-123";
        var expectedResponse = new ChatHistoryResponse
        {
            Messages = new List<ChatMessageResponse>
            {
                new() { Id = Guid.NewGuid(), Message = "Session message", Role = "user", SessionId = sessionId }
            },
            TotalCount = 1,
            Page = 1,
            PageSize = 50,
            TotalPages = 1
        };

        _chatStorageMock.Setup(x => x.GetSessionMessagesAsync(It.IsAny<Guid>(), sessionId, 1, 50))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.GetSessionMessages(sessionId);

        // Assert
        result.Should().BeOfType<ActionResult<ChatHistoryResponse>>();
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var response = okResult.Value.Should().BeOfType<ChatHistoryResponse>().Subject;
        response.Messages.Should().HaveCount(1);
    }

    [Fact]
    public async Task GetSessionMessages_ShouldReturnBadRequest_WhenSessionIdIsEmpty()
    {
        // Act
        var result = await _controller.GetSessionMessages("");

        // Assert
        var badRequestResult = result.Result.Should().BeOfType<BadRequestObjectResult>().Subject;
        badRequestResult.Value.Should().Be("Session ID is required");
    }

    [Fact]
    public async Task DeleteMessage_ShouldReturnOk_WhenMessageDeleted()
    {
        // Arrange
        var messageId = Guid.NewGuid();
        _chatStorageMock.Setup(x => x.DeleteMessageAsync(It.IsAny<Guid>(), messageId))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.DeleteMessage(messageId);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        okResult.Value.Should().BeEquivalentTo(new { message = "Message deleted successfully" });
    }

    [Fact]
    public async Task DeleteMessage_ShouldReturnNotFound_WhenMessageNotFound()
    {
        // Arrange
        var messageId = Guid.NewGuid();
        _chatStorageMock.Setup(x => x.DeleteMessageAsync(It.IsAny<Guid>(), messageId))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.DeleteMessage(messageId);

        // Assert
        var notFoundResult = result.Should().BeOfType<NotFoundObjectResult>().Subject;
        notFoundResult.Value.Should().Be("Message not found or you don't have permission to delete it");
    }

    [Fact]
    public async Task DeleteSession_ShouldReturnOk_WhenSessionDeleted()
    {
        // Arrange
        var sessionId = "session-123";
        _chatStorageMock.Setup(x => x.DeleteSessionAsync(It.IsAny<Guid>(), sessionId))
            .ReturnsAsync(5);

        // Act
        var result = await _controller.DeleteSession(sessionId);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        okResult.Value.Should().BeEquivalentTo(new { message = "Session deleted successfully. 5 messages removed." });
    }

    [Fact]
    public async Task DeleteSession_ShouldReturnBadRequest_WhenSessionIdIsEmpty()
    {
        // Act
        var result = await _controller.DeleteSession("");

        // Assert
        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        badRequestResult.Value.Should().Be("Session ID is required");
    }

    [Fact]
    public async Task UpdateConversationTitle_ShouldReturnOk_WhenTitleUpdated()
    {
        // Arrange
        var sessionId = "session-123";
        var request = new UpdateTitleRequest { Title = "New Title" };
        _chatStorageMock.Setup(x => x.UpdateConversationTitleAsync(It.IsAny<Guid>(), sessionId, request.Title))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.UpdateConversationTitle(sessionId, request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        okResult.Value.Should().BeEquivalentTo(new { message = "Conversation title updated successfully" });
    }

    [Fact]
    public async Task UpdateConversationTitle_ShouldReturnBadRequest_WhenTitleIsEmpty()
    {
        // Arrange
        var sessionId = "session-123";
        var request = new UpdateTitleRequest { Title = "" };

        // Act
        var result = await _controller.UpdateConversationTitle(sessionId, request);

        // Assert
        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        badRequestResult.Value.Should().Be("Title is required");
    }

    [Fact]
    public async Task UpdateConversationTitle_ShouldReturnNotFound_WhenSessionNotFound()
    {
        // Arrange
        var sessionId = "session-123";
        var request = new UpdateTitleRequest { Title = "New Title" };
        _chatStorageMock.Setup(x => x.UpdateConversationTitleAsync(It.IsAny<Guid>(), sessionId, request.Title))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.UpdateConversationTitle(sessionId, request);

        // Assert
        var notFoundResult = result.Should().BeOfType<NotFoundObjectResult>().Subject;
        notFoundResult.Value.Should().Be("Session not found or you don't have permission to update it");
    }
}
