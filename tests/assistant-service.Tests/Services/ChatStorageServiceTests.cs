using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using Xunit;
using AssistantService.Data;
using AssistantService.Models;
using AssistantService.Services;

namespace AssistantService.Tests.Services;

public class ChatStorageServiceTests : IDisposable
{
    private readonly AssistantDbContext _context;
    private readonly Mock<ILogger<ChatStorageService>> _loggerMock;
    private readonly ChatStorageService _service;

    public ChatStorageServiceTests()
    {
        // Create in-memory database for testing
        var options = new DbContextOptionsBuilder<AssistantDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AssistantDbContext(options);
        _loggerMock = new Mock<ILogger<ChatStorageService>>();
        _service = new ChatStorageService(_context, _loggerMock.Object);
    }

    [Fact]
    public async Task SaveMessageAsync_ShouldSaveMessage_WhenValidRequest()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var request = new CreateChatMessageRequest
        {
            Message = "Hello, AI!",
            Role = "user",
            SessionId = "session-123",
            Model = "mistral"
        };

        // Act
        var result = await _service.SaveMessageAsync(userId, request);

        // Assert
        result.Should().NotBeNull();
        result.UserId.Should().Be(userId);
        result.Message.Should().Be(request.Message);
        result.Role.Should().Be(request.Role);
        result.SessionId.Should().Be(request.SessionId);
        result.Model.Should().Be(request.Model);

        // Verify it was saved to database
        var savedMessage = await _context.AiChatMessages.FirstOrDefaultAsync(m => m.Id == result.Id);
        savedMessage.Should().NotBeNull();
        savedMessage!.Message.Should().Be(request.Message);
    }

    [Fact]
    public async Task GetChatHistoryAsync_ShouldReturnPaginatedResults_WhenMessagesExist()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var sessionId = "session-123";

        // Add test messages
        var messages = new List<AiChatMessage>
        {
            new() { UserId = userId, Message = "Message 1", Role = "user", SessionId = sessionId, CreatedAt = DateTime.UtcNow.AddMinutes(-10) },
            new() { UserId = userId, Message = "Response 1", Role = "assistant", SessionId = sessionId, CreatedAt = DateTime.UtcNow.AddMinutes(-9) },
            new() { UserId = userId, Message = "Message 2", Role = "user", SessionId = sessionId, CreatedAt = DateTime.UtcNow.AddMinutes(-5) },
            new() { UserId = userId, Message = "Response 2", Role = "assistant", SessionId = sessionId, CreatedAt = DateTime.UtcNow.AddMinutes(-4) }
        };

        _context.AiChatMessages.AddRange(messages);
        await _context.SaveChangesAsync();

        var request = new ChatHistoryRequest
        {
            SessionId = sessionId,
            Page = 1,
            PageSize = 2
        };

        // Act
        var result = await _service.GetChatHistoryAsync(userId, request);

        // Assert
        result.Should().NotBeNull();
        result.Messages.Should().HaveCount(2);
        result.TotalCount.Should().Be(4);
        result.Page.Should().Be(1);
        result.PageSize.Should().Be(2);
        result.TotalPages.Should().Be(2);

        // Messages should be ordered by creation time
        result.Messages[0].Message.Should().Be("Message 1");
        result.Messages[1].Message.Should().Be("Response 1");
    }

    [Fact]
    public async Task GetChatSessionsAsync_ShouldReturnSessionSummaries_WhenSessionsExist()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var session1 = "session-1";
        var session2 = "session-2";

        var messages = new List<AiChatMessage>
        {
            new() { UserId = userId, Message = "Hello", Role = "user", SessionId = session1, CreatedAt = DateTime.UtcNow.AddHours(-2) },
            new() { UserId = userId, Message = "Hi there", Role = "assistant", SessionId = session1, CreatedAt = DateTime.UtcNow.AddHours(-1) },
            new() { UserId = userId, Message = "How are you?", Role = "user", SessionId = session2, CreatedAt = DateTime.UtcNow.AddMinutes(-30) },
            new() { UserId = userId, Message = "I'm doing well", Role = "assistant", SessionId = session2, CreatedAt = DateTime.UtcNow.AddMinutes(-29) }
        };

        _context.AiChatMessages.AddRange(messages);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetChatSessionsAsync(userId, 1, 10);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);

        // Sessions should be ordered by last message time (most recent first)
        result[0].SessionId.Should().Be(session2);
        result[0].MessageCount.Should().Be(2);
        result[0].LastMessage.Should().Be("I'm doing well");

        result[1].SessionId.Should().Be(session1);
        result[1].MessageCount.Should().Be(2);
        result[1].LastMessage.Should().Be("Hi there");
    }

    [Fact]
    public async Task DeleteMessageAsync_ShouldDeleteMessage_WhenMessageExists()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var message = new AiChatMessage
        {
            UserId = userId,
            Message = "Test message",
            Role = "user",
            SessionId = "session-123"
        };

        _context.AiChatMessages.Add(message);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.DeleteMessageAsync(userId, message.Id);

        // Assert
        result.Should().BeTrue();

        // Verify message was deleted
        var deletedMessage = await _context.AiChatMessages.FirstOrDefaultAsync(m => m.Id == message.Id);
        deletedMessage.Should().BeNull();
    }

    [Fact]
    public async Task DeleteMessageAsync_ShouldReturnFalse_WhenMessageDoesNotExist()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var nonExistentMessageId = Guid.NewGuid();

        // Act
        var result = await _service.DeleteMessageAsync(userId, nonExistentMessageId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task DeleteSessionAsync_ShouldDeleteAllMessagesInSession_WhenSessionExists()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var sessionId = "session-to-delete";

        var messages = new List<AiChatMessage>
        {
            new() { UserId = userId, Message = "Message 1", Role = "user", SessionId = sessionId },
            new() { UserId = userId, Message = "Response 1", Role = "assistant", SessionId = sessionId },
            new() { UserId = userId, Message = "Message 2", Role = "user", SessionId = "other-session" }
        };

        _context.AiChatMessages.AddRange(messages);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.DeleteSessionAsync(userId, sessionId);

        // Assert
        result.Should().Be(2); // Two messages deleted

        // Verify only session messages were deleted
        var remainingMessages = await _context.AiChatMessages.ToListAsync();
        remainingMessages.Should().HaveCount(1);
        remainingMessages[0].SessionId.Should().Be("other-session");
    }

    [Fact]
    public async Task UpdateConversationTitleAsync_ShouldUpdateTitle_WhenSessionExists()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var sessionId = "session-123";
        var newTitle = "Updated Title";

        var messages = new List<AiChatMessage>
        {
            new() { UserId = userId, Message = "Message 1", Role = "user", SessionId = sessionId },
            new() { UserId = userId, Message = "Response 1", Role = "assistant", SessionId = sessionId }
        };

        _context.AiChatMessages.AddRange(messages);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.UpdateConversationTitleAsync(userId, sessionId, newTitle);

        // Assert
        result.Should().BeTrue();

        // Verify title was updated for all messages in session
        var updatedMessages = await _context.AiChatMessages
            .Where(m => m.SessionId == sessionId)
            .ToListAsync();

        updatedMessages.Should().AllSatisfy(m => m.ConversationTitle.Should().Be(newTitle));
    }

    [Fact]
    public async Task GetNextSequenceNumberAsync_ShouldReturnOne_WhenSessionIsEmpty()
    {
        // Arrange
        var sessionId = "empty-session";

        // Act
        var result = await _service.GetNextSequenceNumberAsync(sessionId);

        // Assert
        result.Should().Be(1);
    }

    [Fact]
    public async Task GetNextSequenceNumberAsync_ShouldReturnCorrectSequence_WhenMessagesExist()
    {
        // Arrange
        var sessionId = "session-with-messages";

        var messages = new List<AiChatMessage>
        {
            new() { SessionId = sessionId, MessageSequence = 1 },
            new() { SessionId = sessionId, MessageSequence = 3 },
            new() { SessionId = sessionId, MessageSequence = 2 }
        };

        _context.AiChatMessages.AddRange(messages);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetNextSequenceNumberAsync(sessionId);

        // Assert
        result.Should().Be(4); // Max sequence (3) + 1
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
