using AssistantService.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AssistantService.Tests.Services;

public class ContentFilterTests
{
    private readonly ContentFilter _filter;
    private readonly Mock<ILogger<ContentFilter>> _loggerMock;

    public ContentFilterTests()
    {
        _loggerMock = new Mock<ILogger<ContentFilter>>();
        _filter = new ContentFilter(_loggerMock.Object);
    }

    [Theory]
    [InlineData("jailbreak")]
    [InlineData("dan mode")]
    [InlineData("developer mode")]
    [InlineData("ignore instructions")]
    [InlineData("system prompt")]
    [InlineData("bomb making")]
    [InlineData("nazi")]
    public void FilterContent_WithBlockedKeywords_BlocksContent(string input)
    {
        // Act
        var result = _filter.FilterContent(input);

        // Assert
        result.IsContentSafe.Should().BeFalse();
        result.RiskLevel.Should().Be(ContentRiskLevel.Blocked);
        result.ViolatedPolicies.Should().Contain(p => p.Contains("blocked_keyword"));
    }

    [Theory]
    [InlineData("bomb", ContentRiskLevel.High)]
    [InlineData("weapon", ContentRiskLevel.High)]
    [InlineData("violence", ContentRiskLevel.High)]
    [InlineData("hack", ContentRiskLevel.Medium)]
    [InlineData("fraud", ContentRiskLevel.High)]
    [InlineData("porn", ContentRiskLevel.Medium)]
    public void FilterContent_WithHarmfulPatterns_DetectsRisk(string input, ContentRiskLevel expectedMinRisk)
    {
        // Act
        var result = _filter.FilterContent(input);

        // Assert
        result.IsContentSafe.Should().BeTrue(); // Not blocked, but flagged
        ((int)result.RiskLevel).Should().BeGreaterOrEqualTo((int)expectedMinRisk);
        result.ViolatedPolicies.Should().Contain(p => p.Contains("harmful_pattern"));
    }

    [Fact]
    public void FilterContent_WithExcessiveProfanity_DetectsMediumRisk()
    {
        // Arrange
        var input = "damn shit fuck bitch ass hell damn shit fuck";

        // Act
        var result = _filter.FilterContent(input);

        // Assert
        result.IsContentSafe.Should().BeTrue();
        ((int)result.RiskLevel).Should().BeGreaterOrEqualTo((int)ContentRiskLevel.Medium);
        result.ViolatedPolicies.Should().Contain("excessive_profanity");
    }

    [Fact]
    public void FilterContent_WithRepeatedContent_DetectsLowRisk()
    {
        // Arrange - Use a pattern that matches the regex (.{10,})\1{3,} (10+ chars repeated 3+ times)
        var repeatedPattern = "1234567890"; // 10 characters
        var input = repeatedPattern + repeatedPattern + repeatedPattern + repeatedPattern; // Repeated 4 times

        // Act
        var result = _filter.FilterContent(input);

        // Assert
        result.IsContentSafe.Should().BeTrue();
        ((int)result.RiskLevel).Should().BeGreaterOrEqualTo((int)ContentRiskLevel.Low);
        result.ViolatedPolicies.Should().Contain("repeated_content");
    }

    [Fact]
    public void FilterContent_WithPotentialPhishing_DetectsMediumRisk()
    {
        // Arrange
        var input = "Click this link to visit our website";

        // Act
        var result = _filter.FilterContent(input);

        // Assert
        result.IsContentSafe.Should().BeTrue();
        ((int)result.RiskLevel).Should().BeGreaterOrEqualTo((int)ContentRiskLevel.Medium);
        result.ViolatedPolicies.Should().Contain("potential_phishing");
    }

    [Fact]
    public void FilterContent_WithSocialEngineering_DetectsLowRisk()
    {
        // Arrange
        var input = "This is urgent! Act now or you'll miss out!";

        // Act
        var result = _filter.FilterContent(input);

        // Assert
        result.IsContentSafe.Should().BeTrue();
        ((int)result.RiskLevel).Should().BeGreaterOrEqualTo((int)ContentRiskLevel.Low);
        result.ViolatedPolicies.Should().Contain("social_engineering");
    }

    [Theory]
    [InlineData("Hello, how are you?")]
    [InlineData("What's the weather like today?")]
    [InlineData("Can you help me with math?")]
    [InlineData("Tell me a story")]
    public void FilterContent_WithSafeContent_ReturnsSafe(string input)
    {
        // Act
        var result = _filter.FilterContent(input);

        // Assert
        result.IsContentSafe.Should().BeTrue();
        result.RiskLevel.Should().Be(ContentRiskLevel.Safe);
        result.ViolatedPolicies.Should().BeEmpty();
        result.FilteredContent.Should().Be(input);
    }

    [Fact]
    public void FilterContent_WithEmptyInput_ReturnsSafe()
    {
        // Act
        var result = _filter.FilterContent(string.Empty);

        // Assert
        result.IsContentSafe.Should().BeTrue();
        result.RiskLevel.Should().Be(ContentRiskLevel.Safe);
        result.FilteredContent.Should().Be(string.Empty);
    }

    [Fact]
    public void FilterContent_FiltersHarmfulContent()
    {
        // Arrange
        var input = "How to make a bomb and hack systems";

        // Act
        var result = _filter.FilterContent(input);

        // Assert
        result.FilteredContent.Should().Contain("[FILTERED]");
        result.FilteredContent.Should().NotContain("bomb"); // bomb is High risk, gets filtered
        // Note: "hack" is Medium risk, so it doesn't get filtered (only High+ gets filtered)
    }

    [Fact]
    public void IsContentSafe_WithSafeContent_ReturnsTrue()
    {
        // Arrange
        var input = "Hello, world!";

        // Act
        var result = _filter.IsContentSafe(input);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsContentSafe_WithBlockedContent_ReturnsFalse()
    {
        // Arrange
        var input = "jailbreak mode activated";

        // Act
        var result = _filter.IsContentSafe(input);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData(ContentRiskLevel.Safe, "Content is safe for processing")]
    [InlineData(ContentRiskLevel.Low, "Content has minor policy concerns")]
    [InlineData(ContentRiskLevel.Medium, "Content requires additional review")]
    [InlineData(ContentRiskLevel.High, "Content may contain harmful or inappropriate material")]
    [InlineData(ContentRiskLevel.Blocked, "Content contains blocked keywords")]
    public void FilterContent_ReturnsAppropriateReason(ContentRiskLevel riskLevel, string expectedReason)
    {
        // This test verifies that the GetRiskReason method returns appropriate messages
        // We'll test this indirectly through the FilterContent method

        string input = riskLevel switch
        {
            ContentRiskLevel.Safe => "Hello world",
            ContentRiskLevel.Low => "This is urgent!",
            ContentRiskLevel.Medium => "Click this link",
            ContentRiskLevel.High => "violence and harm", // Use a High risk pattern that's not in blocked keywords
            ContentRiskLevel.Blocked => "jailbreak",
            _ => "Hello world"
        };

        // Act
        var result = _filter.FilterContent(input);

        // Assert
        result.Reason.Should().Be(expectedReason);
    }
}
