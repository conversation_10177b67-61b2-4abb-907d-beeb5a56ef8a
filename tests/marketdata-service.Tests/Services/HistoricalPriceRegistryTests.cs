using FluentAssertions;
using MarketDataService.Interfaces;
using MarketDataService.Services;
using MarketDataService.Tests.TestHelpers;
using Xunit;

namespace MarketDataService.Tests.Services;

public class HistoricalPriceRegistryTests
{
    [Fact]
    public void Constructor_WithProviders_ShouldInitializeCorrectly()
    {
        // Arrange
        var providers = new[]
        {
            new TestHelpers.MockHistoricalPriceProvider { BrokerId = "broker1" },
            new TestHelpers.MockHistoricalPriceProvider { BrokerId = "broker2" }
        };

        // Act
        var registry = new HistoricalPriceRegistry(providers);

        // Assert
        registry.Should().NotBeNull();
    }

    [Fact]
    public void GetProvider_WithValidBrokerId_ShouldReturnProvider()
    {
        // Arrange
        var provider1 = new TestHelpers.MockHistoricalPriceProvider { BrokerId = "broker1" };
        var provider2 = new TestHelpers.MockHistoricalPriceProvider { BrokerId = "broker2" };
        var registry = new HistoricalPriceRegistry(new[] { provider1, provider2 });

        // Act
        var result = registry.GetProvider("broker1");

        // Assert
        result.Should().NotBeNull();
        result!.BrokerId.Should().Be("broker1");
        result.Should().BeSameAs(provider1);
    }

    [Fact]
    public void GetProvider_WithValidBrokerIdLowerCase_ShouldReturnProvider()
    {
        // Arrange
        var provider = new TestHelpers.MockHistoricalPriceProvider { BrokerId = "BROKER1" };
        var registry = new HistoricalPriceRegistry(new[] { provider });

        // Act
        var result = registry.GetProvider("broker1");

        // Assert
        result.Should().NotBeNull();
        result!.BrokerId.Should().Be("BROKER1");
    }

    [Fact]
    public void GetProvider_WithInvalidBrokerId_ShouldReturnNull()
    {
        // Arrange
        var providers = new[]
        {
            new TestHelpers.MockHistoricalPriceProvider { BrokerId = "broker1" },
            new TestHelpers.MockHistoricalPriceProvider { BrokerId = "broker2" }
        };
        var registry = new HistoricalPriceRegistry(providers);

        // Act
        var result = registry.GetProvider("nonexistent");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void Constructor_WithDuplicateBrokerIds_ShouldThrowException()
    {
        // Arrange
        var providers = new[]
        {
            new TestHelpers.MockHistoricalPriceProvider { BrokerId = "broker1" },
            new TestHelpers.MockHistoricalPriceProvider { BrokerId = "broker1" } // Duplicate
        };

        // Act & Assert
        var act = () => new HistoricalPriceRegistry(providers);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Constructor_WithEmptyProviders_ShouldInitializeWithEmptyDictionary()
    {
        // Arrange & Act
        var registry = new HistoricalPriceRegistry(Array.Empty<TestHelpers.MockHistoricalPriceProvider>());

        // Assert
        registry.GetProvider("any").Should().BeNull();
    }
}
