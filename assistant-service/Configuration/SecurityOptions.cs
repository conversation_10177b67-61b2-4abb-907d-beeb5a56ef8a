namespace AssistantService.Configuration;

public class SecurityOptions
{
    public const string SectionName = "Security";

    /// <summary>
    /// Enable or disable prompt injection detection
    /// </summary>
    public bool EnablePromptInjectionDetection { get; set; } = true;

    /// <summary>
    /// Enable or disable content filtering
    /// </summary>
    public bool EnableContentFiltering { get; set; } = true;

    /// <summary>
    /// Enable or disable rate limiting
    /// </summary>
    public bool EnableRateLimiting { get; set; } = true;

    /// <summary>
    /// Maximum allowed input length in characters
    /// </summary>
    public int MaxInputLength { get; set; } = 4000;

    /// <summary>
    /// Prompt injection detection settings
    /// </summary>
    public PromptInjectionOptions PromptInjection { get; set; } = new();

    /// <summary>
    /// Content filtering settings
    /// </summary>
    public ContentFilterOptions ContentFilter { get; set; } = new();

    /// <summary>
    /// Rate limiting settings
    /// </summary>
    public RateLimitOptions RateLimit { get; set; } = new();
}

public class PromptInjectionOptions
{
    /// <summary>
    /// Block requests with critical risk level
    /// </summary>
    public bool BlockCriticalRisk { get; set; } = true;

    /// <summary>
    /// Block requests with high risk level
    /// </summary>
    public bool BlockHighRisk { get; set; } = true;

    /// <summary>
    /// Log all detected injection attempts
    /// </summary>
    public bool LogDetections { get; set; } = true;

    /// <summary>
    /// Custom patterns to detect (regex patterns)
    /// </summary>
    public List<string> CustomPatterns { get; set; } = new();

    /// <summary>
    /// Sensitivity level for heuristic detection (0.0 to 1.0)
    /// </summary>
    public double HeuristicSensitivity { get; set; } = 0.5;
}

public class ContentFilterOptions
{
    /// <summary>
    /// Block content with blocked risk level
    /// </summary>
    public bool BlockUnsafeContent { get; set; } = true;

    /// <summary>
    /// Log all content filter violations
    /// </summary>
    public bool LogViolations { get; set; } = true;

    /// <summary>
    /// Custom blocked keywords
    /// </summary>
    public List<string> CustomBlockedKeywords { get; set; } = new();

    /// <summary>
    /// Custom harmful patterns (regex patterns with risk levels)
    /// </summary>
    public Dictionary<string, string> CustomHarmfulPatterns { get; set; } = new();

    /// <summary>
    /// Enable profanity filtering
    /// </summary>
    public bool EnableProfanityFilter { get; set; } = true;

    /// <summary>
    /// Maximum allowed profanity count before flagging
    /// </summary>
    public int MaxProfanityCount { get; set; } = 5;
}

public class RateLimitOptions
{
    /// <summary>
    /// Default requests per minute limit
    /// </summary>
    public int DefaultRequestsPerMinute { get; set; } = 5;

    /// <summary>
    /// Default requests per hour limit
    /// </summary>
    public int DefaultRequestsPerHour { get; set; } = 50;

    /// <summary>
    /// Default requests per day limit
    /// </summary>
    public int DefaultRequestsPerDay { get; set; } = 200;

    /// <summary>
    /// Endpoint-specific rate limits
    /// </summary>
    public Dictionary<string, EndpointRateLimit> EndpointLimits { get; set; } = new()
    {
        { "/api/chat", new EndpointRateLimit { RequestsPerMinute = 10, RequestsPerHour = 100, RequestsPerDay = 500 } },
        { "/api/assistant/ask", new EndpointRateLimit { RequestsPerMinute = 15, RequestsPerHour = 150, RequestsPerDay = 750 } }
    };

    /// <summary>
    /// Auto-block threshold (requests per minute that trigger automatic blocking)
    /// </summary>
    public int AutoBlockThreshold { get; set; } = 50;

    /// <summary>
    /// Auto-block duration in minutes
    /// </summary>
    public int AutoBlockDurationMinutes { get; set; } = 60;

    /// <summary>
    /// Rapid-fire detection threshold (requests in 10 seconds)
    /// </summary>
    public int RapidFireThreshold { get; set; } = 10;

    /// <summary>
    /// Rapid-fire block duration in minutes
    /// </summary>
    public int RapidFireBlockDurationMinutes { get; set; } = 5;
}

public class EndpointRateLimit
{
    public int RequestsPerMinute { get; set; }
    public int RequestsPerHour { get; set; }
    public int RequestsPerDay { get; set; }
}
