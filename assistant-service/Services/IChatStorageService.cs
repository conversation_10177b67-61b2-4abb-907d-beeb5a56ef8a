using AssistantService.Models;

namespace AssistantService.Services;

/// <summary>
/// Interface for chat storage operations
/// </summary>
public interface IChatStorageService
{
    /// <summary>
    /// Save a chat message to the database
    /// </summary>
    /// <param name="userId">User ID from authentication</param>
    /// <param name="request">Chat message request</param>
    /// <returns>The saved chat message</returns>
    Task<ChatMessageResponse> SaveMessageAsync(Guid userId, CreateChatMessageRequest request);

    /// <summary>
    /// Get chat history for a user with pagination
    /// </summary>
    /// <param name="userId">User ID from authentication</param>
    /// <param name="request">Chat history request with filters</param>
    /// <returns>Paginated chat history</returns>
    Task<ChatHistoryResponse> GetChatHistoryAsync(Guid userId, ChatHistoryRequest request);

    /// <summary>
    /// Get chat sessions for a user
    /// </summary>
    /// <param name="userId">User ID from authentication</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>List of chat sessions</returns>
    Task<List<ChatSessionResponse>> GetChatSessionsAsync(Guid userId, int page = 1, int pageSize = 20);

    /// <summary>
    /// Get messages for a specific session
    /// </summary>
    /// <param name="userId">User ID from authentication</param>
    /// <param name="sessionId">Session ID</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Messages in the session</returns>
    Task<ChatHistoryResponse> GetSessionMessagesAsync(Guid userId, string sessionId, int page = 1, int pageSize = 50);

    /// <summary>
    /// Delete a chat message
    /// </summary>
    /// <param name="userId">User ID from authentication</param>
    /// <param name="messageId">Message ID to delete</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteMessageAsync(Guid userId, Guid messageId);

    /// <summary>
    /// Delete all messages in a session
    /// </summary>
    /// <param name="userId">User ID from authentication</param>
    /// <param name="sessionId">Session ID to delete</param>
    /// <returns>Number of messages deleted</returns>
    Task<int> DeleteSessionAsync(Guid userId, string sessionId);

    /// <summary>
    /// Update conversation title
    /// </summary>
    /// <param name="userId">User ID from authentication</param>
    /// <param name="sessionId">Session ID</param>
    /// <param name="title">New title</param>
    /// <returns>True if updated successfully</returns>
    Task<bool> UpdateConversationTitleAsync(Guid userId, string sessionId, string title);

    /// <summary>
    /// Get the next message sequence number for a session
    /// </summary>
    /// <param name="sessionId">Session ID</param>
    /// <returns>Next sequence number</returns>
    Task<int> GetNextSequenceNumberAsync(string? sessionId);
}
