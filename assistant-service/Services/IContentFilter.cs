namespace AssistantService.Services;

public interface IContentFilter
{
    /// <summary>
    /// Filters content to ensure it's safe for processing
    /// </summary>
    /// <param name="content">The content to filter</param>
    /// <returns>Filtering result with safety assessment</returns>
    ContentFilterResult FilterContent(string content);

    /// <summary>
    /// Checks if the content is safe for AI processing
    /// </summary>
    /// <param name="content">The content to check</param>
    /// <returns>True if content is safe, false otherwise</returns>
    bool IsContentSafe(string content);
}

public class ContentFilterResult
{
    public bool IsContentSafe { get; set; }
    public List<string> ViolatedPolicies { get; set; } = new();
    public string? FilteredContent { get; set; }
    public ContentRiskLevel RiskLevel { get; set; }
    public string? Reason { get; set; }
}

public enum ContentRiskLevel
{
    Safe = 0,
    Low = 1,
    Medium = 2,
    High = 3,
    Blocked = 4
}
