using Microsoft.EntityFrameworkCore;
using AssistantService.Data;
using AssistantService.Models;

namespace AssistantService.Services;

/// <summary>
/// Implementation of chat storage service
/// </summary>
public class ChatStorageService(AssistantDbContext context, ILogger<ChatStorageService> logger) : IChatStorageService
{
    private readonly AssistantDbContext _context = context;
    private readonly ILogger<ChatStorageService> _logger = logger;

    public async Task<ChatMessageResponse> SaveMessageAsync(Guid userId, CreateChatMessageRequest request)
    {
        try
        {
            var sequenceNumber = await GetNextSequenceNumberAsync(request.SessionId);

            var message = new AiChatMessage
            {
                UserId = userId,
                Message = request.Message,
                Role = request.Role,
                SessionId = request.SessionId,
                Model = request.Model ?? "mistral",
                ConversationTitle = request.ConversationTitle,
                MessageSequence = sequenceNumber,
                Metadata = request.Metadata
            };

            _context.AiChatMessages.Add(message);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Saved chat message {MessageId} for user {UserId} in session {SessionId}", 
                message.Id, userId, request.SessionId);

            return MapToResponse(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving chat message for user {UserId}", userId);
            throw;
        }
    }

    public async Task<ChatHistoryResponse> GetChatHistoryAsync(Guid userId, ChatHistoryRequest request)
    {
        try
        {
            var query = _context.AiChatMessages
                .Where(m => m.UserId == userId);

            // Apply filters
            if (!string.IsNullOrEmpty(request.SessionId))
            {
                query = query.Where(m => m.SessionId == request.SessionId);
            }

            if (request.FromDate.HasValue)
            {
                query = query.Where(m => m.CreatedAt >= request.FromDate.Value);
            }

            if (request.ToDate.HasValue)
            {
                query = query.Where(m => m.CreatedAt <= request.ToDate.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply pagination and ordering
            var messages = await query
                .OrderBy(m => m.CreatedAt)
                .ThenBy(m => m.MessageSequence)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

            return new ChatHistoryResponse
            {
                Messages = messages.Select(MapToResponse).ToList(),
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                TotalPages = totalPages
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chat history for user {UserId}", userId);
            throw;
        }
    }

    public async Task<List<ChatSessionResponse>> GetChatSessionsAsync(Guid userId, int page = 1, int pageSize = 20)
    {
        try
        {
            var sessions = await _context.AiChatMessages
                .Where(m => m.UserId == userId && !string.IsNullOrEmpty(m.SessionId))
                .GroupBy(m => m.SessionId)
                .Select(g => new ChatSessionResponse
                {
                    SessionId = g.Key!,
                    ConversationTitle = g.OrderByDescending(m => m.CreatedAt).First().ConversationTitle,
                    LastMessageAt = g.Max(m => m.CreatedAt),
                    MessageCount = g.Count(),
                    LastMessage = g.OrderByDescending(m => m.CreatedAt).First().Message
                })
                .OrderByDescending(s => s.LastMessageAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return sessions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chat sessions for user {UserId}", userId);
            throw;
        }
    }

    public async Task<ChatHistoryResponse> GetSessionMessagesAsync(Guid userId, string sessionId, int page = 1, int pageSize = 50)
    {
        try
        {
            var query = _context.AiChatMessages
                .Where(m => m.UserId == userId && m.SessionId == sessionId);

            var totalCount = await query.CountAsync();

            var messages = await query
                .OrderBy(m => m.CreatedAt)
                .ThenBy(m => m.MessageSequence)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            return new ChatHistoryResponse
            {
                Messages = messages.Select(MapToResponse).ToList(),
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = totalPages
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting session messages for user {UserId}, session {SessionId}", userId, sessionId);
            throw;
        }
    }

    public async Task<bool> DeleteMessageAsync(Guid userId, Guid messageId)
    {
        try
        {
            var message = await _context.AiChatMessages
                .FirstOrDefaultAsync(m => m.Id == messageId && m.UserId == userId);

            if (message == null)
            {
                return false;
            }

            _context.AiChatMessages.Remove(message);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted chat message {MessageId} for user {UserId}", messageId, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting chat message {MessageId} for user {UserId}", messageId, userId);
            throw;
        }
    }

    public async Task<int> DeleteSessionAsync(Guid userId, string sessionId)
    {
        try
        {
            var messages = await _context.AiChatMessages
                .Where(m => m.UserId == userId && m.SessionId == sessionId)
                .ToListAsync();

            if (!messages.Any())
            {
                return 0;
            }

            _context.AiChatMessages.RemoveRange(messages);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted {Count} messages from session {SessionId} for user {UserId}", 
                messages.Count, sessionId, userId);
            return messages.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting session {SessionId} for user {UserId}", sessionId, userId);
            throw;
        }
    }

    public async Task<bool> UpdateConversationTitleAsync(Guid userId, string sessionId, string title)
    {
        try
        {
            var messages = await _context.AiChatMessages
                .Where(m => m.UserId == userId && m.SessionId == sessionId)
                .ToListAsync();

            if (!messages.Any())
            {
                return false;
            }

            foreach (var message in messages)
            {
                message.ConversationTitle = title;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated conversation title for session {SessionId} for user {UserId}", sessionId, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating conversation title for session {SessionId} for user {UserId}", sessionId, userId);
            throw;
        }
    }

    public async Task<int> GetNextSequenceNumberAsync(string? sessionId)
    {
        if (string.IsNullOrEmpty(sessionId))
        {
            return 1;
        }

        try
        {
            var maxSequence = await _context.AiChatMessages
                .Where(m => m.SessionId == sessionId)
                .MaxAsync(m => (int?)m.MessageSequence) ?? 0;

            return maxSequence + 1;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting next sequence number for session {SessionId}", sessionId);
            return 1;
        }
    }

    private static ChatMessageResponse MapToResponse(AiChatMessage message)
    {
        return new ChatMessageResponse
        {
            Id = message.Id,
            UserId = message.UserId,
            Message = message.Message,
            Role = message.Role,
            SessionId = message.SessionId,
            CreatedAt = message.CreatedAt,
            Model = message.Model,
            Metadata = message.Metadata,
            ConversationTitle = message.ConversationTitle,
            MessageSequence = message.MessageSequence
        };
    }
}
