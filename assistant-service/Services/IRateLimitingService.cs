namespace AssistantService.Services;

public interface IRateLimitingService
{
    /// <summary>
    /// Checks if a request is allowed based on rate limiting rules
    /// </summary>
    /// <param name="clientId">Unique identifier for the client</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    /// <returns>Rate limiting result</returns>
    RateLimitResult CheckRateLimit(string clientId, string endpoint);

    /// <summary>
    /// Records a request for rate limiting tracking
    /// </summary>
    /// <param name="clientId">Unique identifier for the client</param>
    /// <param name="endpoint">The endpoint being accessed</param>
    void RecordRequest(string clientId, string endpoint);

    /// <summary>
    /// Checks if a client is currently blocked due to suspicious activity
    /// </summary>
    /// <param name="clientId">Unique identifier for the client</param>
    /// <returns>True if client is blocked, false otherwise</returns>
    bool IsClientBlocked(string clientId);

    /// <summary>
    /// Blocks a client for a specified duration
    /// </summary>
    /// <param name="clientId">Unique identifier for the client</param>
    /// <param name="duration">Duration of the block</param>
    /// <param name="reason">Reason for blocking</param>
    void BlockClient(string clientId, TimeSpan duration, string reason);
}

public class RateLimitResult
{
    public bool IsAllowed { get; set; }
    public int RequestsRemaining { get; set; }
    public TimeSpan ResetTime { get; set; }
    public string? Reason { get; set; }
    public RateLimitType LimitType { get; set; }
}

public enum RateLimitType
{
    None = 0,
    PerMinute = 1,
    PerHour = 2,
    PerDay = 3,
    Blocked = 4
}
