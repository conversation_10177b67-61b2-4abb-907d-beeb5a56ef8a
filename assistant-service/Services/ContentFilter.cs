using System.Text.RegularExpressions;

namespace AssistantService.Services;

public class ContentFilter(ILogger<ContentFilter> logger) : IContentFilter
{
    private readonly ILogger<ContentFilter> _logger = logger;

    // Harmful content patterns
    private static readonly Dictionary<string, ContentRiskLevel> HarmfulPatterns = new()
    {
        // Violence and weapons
        { @"\b(bomb|explosive|weapon|gun|knife|kill|murder|violence)\b", ContentRiskLevel.High },
        { @"\b(assassination|terrorist|attack|harm|hurt|damage)\b", ContentRiskLevel.High },
        
        // Illegal activities
        { @"\b(drug|cocaine|heroin|marijuana|illegal|crime|steal|rob)\b", ContentRiskLevel.High },
        { @"\b(hack|crack|piracy|fraud|scam|cheat)\b", ContentRiskLevel.Medium },
        
        // Hate speech and discrimination
        { @"\b(racist|sexist|homophobic|hate|discrimination)\b", ContentRiskLevel.High },
        { @"\b(nazi|fascist|supremacist|bigot)\b", ContentRiskLevel.Blocked },
        
        // Self-harm
        { @"\b(suicide|self.harm|cut.myself|end.my.life)\b", ContentRiskLevel.Blocked },
        { @"\b(depression|anxiety|mental.health)\b", ContentRiskLevel.Low }, // Not blocked, but flagged
        
        // Adult content
        { @"\b(porn|sex|nude|naked|explicit)\b", ContentRiskLevel.Medium },
        { @"\b(adult|mature|nsfw)\b", ContentRiskLevel.Low },
        
        // Financial fraud
        { @"\b(credit.card|bank.account|social.security|identity.theft)\b", ContentRiskLevel.High },
        { @"\b(phishing|scam|fraud|money.laundering)\b", ContentRiskLevel.High },
        
        // Privacy violations
        { @"\b(personal.information|private.data|confidential|secret)\b", ContentRiskLevel.Medium },
        { @"\b(password|login|credentials|access.token)\b", ContentRiskLevel.Medium },
        
        // System manipulation
        { @"\b(admin|root|system|database|server|network)\b", ContentRiskLevel.Medium },
        { @"\b(bypass|override|disable|exploit|vulnerability)\b", ContentRiskLevel.High }
    };

    // Blocked keywords that should never be processed
    private static readonly HashSet<string> BlockedKeywords = new(StringComparer.OrdinalIgnoreCase)
    {
        "jailbreak", "dan mode", "developer mode", "evil mode",
        "ignore instructions", "forget instructions", "override instructions",
        "system prompt", "reveal prompt", "show prompt",
        "execute code", "run script", "eval(", "system(",
        "bomb making", "terrorist attack", "kill someone",
        "suicide methods", "self harm", "end my life",
        "nazi", "hitler", "holocaust denial", "white supremacy"
    };

    public ContentFilterResult FilterContent(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
        {
            return new ContentFilterResult
            {
                IsContentSafe = true,
                FilteredContent = string.Empty,
                RiskLevel = ContentRiskLevel.Safe
            };
        }

        var result = new ContentFilterResult();
        var normalizedContent = content.ToLowerInvariant();
        var maxRiskLevel = ContentRiskLevel.Safe;
        var violatedPolicies = new List<string>();

        // Check for blocked keywords first
        foreach (var keyword in BlockedKeywords)
        {
            if (normalizedContent.Contains(keyword.ToLowerInvariant()))
            {
                violatedPolicies.Add($"blocked_keyword: {keyword}");
                maxRiskLevel = ContentRiskLevel.Blocked;
                _logger.LogWarning("Blocked keyword detected: {Keyword}", keyword);
            }
        }

        // If already blocked, return immediately
        if (maxRiskLevel == ContentRiskLevel.Blocked)
        {
            result.IsContentSafe = false;
            result.ViolatedPolicies = violatedPolicies;
            result.RiskLevel = maxRiskLevel;
            result.Reason = "Content contains blocked keywords";
            return result;
        }

        // Check harmful patterns
        foreach (var pattern in HarmfulPatterns)
        {
            try
            {
                if (Regex.IsMatch(normalizedContent, pattern.Key, RegexOptions.IgnoreCase))
                {
                    violatedPolicies.Add($"harmful_pattern: {pattern.Key}");
                    if (pattern.Value > maxRiskLevel)
                    {
                        maxRiskLevel = pattern.Value;
                    }
                }
            }
            catch (RegexMatchTimeoutException)
            {
                _logger.LogWarning("Regex timeout for pattern: {Pattern}", pattern.Key);
            }
        }

        // Additional content checks
        maxRiskLevel = (ContentRiskLevel)Math.Max((int)maxRiskLevel, (int)CheckContentHeuristics(content, violatedPolicies));

        // Filter the content if needed
        var filteredContent = maxRiskLevel > ContentRiskLevel.Safe ? FilterHarmfulContent(content) : content;

        result.IsContentSafe = maxRiskLevel < ContentRiskLevel.Blocked;
        result.ViolatedPolicies = violatedPolicies;
        result.FilteredContent = filteredContent;
        result.RiskLevel = maxRiskLevel;
        result.Reason = GetRiskReason(maxRiskLevel, violatedPolicies);

        if (!result.IsContentSafe)
        {
            _logger.LogWarning("Unsafe content detected. Risk: {RiskLevel}, Violations: {Violations}", 
                maxRiskLevel, string.Join(", ", violatedPolicies));
        }

        return result;
    }

    public bool IsContentSafe(string content)
    {
        var result = FilterContent(content);
        return result.IsContentSafe;
    }

    private static ContentRiskLevel CheckContentHeuristics(string content, List<string> violatedPolicies)
    {
        var riskLevel = ContentRiskLevel.Safe;

        // Check for excessive profanity
        var profanityCount = Regex.Matches(content, @"\b(damn|hell|shit|fuck|bitch|ass)\b", RegexOptions.IgnoreCase).Count;
        if (profanityCount > 5)
        {
            violatedPolicies.Add("excessive_profanity");
            riskLevel = (ContentRiskLevel)Math.Max((int)riskLevel, (int)ContentRiskLevel.Medium);
        }

        // Check for potential spam (repeated patterns)
        if (Regex.IsMatch(content, @"(.{10,})\1{3,}"))
        {
            violatedPolicies.Add("repeated_content");
            riskLevel = (ContentRiskLevel)Math.Max((int)riskLevel, (int)ContentRiskLevel.Low);
        }

        // Check for potential phishing attempts
        if (Regex.IsMatch(content, @"(click|visit|go to).*(link|url|website)", RegexOptions.IgnoreCase))
        {
            violatedPolicies.Add("potential_phishing");
            riskLevel = (ContentRiskLevel)Math.Max((int)riskLevel, (int)ContentRiskLevel.Medium);
        }

        // Check for social engineering
        if (Regex.IsMatch(content, @"(urgent|emergency|immediate|act now|limited time)", RegexOptions.IgnoreCase))
        {
            violatedPolicies.Add("social_engineering");
            riskLevel = (ContentRiskLevel)Math.Max((int)riskLevel, (int)ContentRiskLevel.Low);
        }

        return riskLevel;
    }

    private static string FilterHarmfulContent(string content)
    {
        var filtered = content;

        // Replace blocked keywords with placeholders
        foreach (var keyword in BlockedKeywords)
        {
            filtered = Regex.Replace(filtered, Regex.Escape(keyword), "[FILTERED]", RegexOptions.IgnoreCase);
        }

        // Filter out harmful patterns
        foreach (var pattern in HarmfulPatterns.Where(p => p.Value >= ContentRiskLevel.High))
        {
            try
            {
                filtered = Regex.Replace(filtered, pattern.Key, "[FILTERED]", RegexOptions.IgnoreCase);
            }
            catch (RegexMatchTimeoutException)
            {
                // Skip problematic patterns
            }
        }

        return filtered;
    }

    private static string GetRiskReason(ContentRiskLevel riskLevel, List<string> violations)
    {
        return riskLevel switch
        {
            ContentRiskLevel.Blocked => "Content contains blocked keywords or extremely harmful content",
            ContentRiskLevel.High => "Content may contain harmful or inappropriate material",
            ContentRiskLevel.Medium => "Content requires additional review",
            ContentRiskLevel.Low => "Content has minor policy concerns",
            ContentRiskLevel.Safe => "Content is safe for processing",
            _ => "Unknown risk level"
        };
    }
}
