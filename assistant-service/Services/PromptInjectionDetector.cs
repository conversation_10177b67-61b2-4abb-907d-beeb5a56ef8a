using System.Text.RegularExpressions;
using Microsoft.Extensions.Caching.Memory;

namespace AssistantService.Services;

public class PromptInjectionDetector(IMemoryCache cache, ILogger<PromptInjectionDetector> logger) : IPromptInjectionDetector
{
    private readonly IMemoryCache _cache = cache;
    private readonly ILogger<PromptInjectionDetector> _logger = logger;
    
    // Common prompt injection patterns
    private static readonly Dictionary<string, RiskLevel> InjectionPatterns = new()
    {
        // Direct instruction overrides
        { @"ignore\s+(all\s+)?previous\s+instructions?", RiskLevel.High },
        { @"forget\s+(all\s+)?previous\s+instructions?", RiskLevel.High },
        { @"disregard\s+(all\s+)?previous\s+instructions?", RiskLevel.High },
        { @"override\s+(all\s+)?previous\s+instructions?", RiskLevel.High },
        
        // Role manipulation
        { @"you\s+are\s+now\s+", RiskLevel.High },
        { @"act\s+as\s+(if\s+you\s+are\s+)?", RiskLevel.Medium },
        { @"pretend\s+(to\s+be\s+|you\s+are\s+)", RiskLevel.Medium },
        { @"roleplay\s+as\s+", RiskLevel.Medium },
        
        // System prompt extraction
        { @"what\s+(are\s+|were\s+)?your\s+(initial\s+|original\s+)?instructions?", RiskLevel.High },
        { @"show\s+me\s+your\s+(system\s+)?prompt", RiskLevel.High },
        { @"reveal\s+your\s+(system\s+)?prompt", RiskLevel.High },
        { @"what\s+is\s+your\s+(system\s+)?prompt", RiskLevel.High },
        
        // Jailbreak attempts
        { @"developer\s+mode", RiskLevel.High },
        { @"jailbreak", RiskLevel.Critical },
        { @"dan\s+mode", RiskLevel.High },
        { @"evil\s+mode", RiskLevel.High },
        
        // Code execution attempts
        { @"execute\s+code", RiskLevel.Critical },
        { @"run\s+script", RiskLevel.Critical },
        { @"eval\s*\(", RiskLevel.Critical },
        { @"system\s*\(", RiskLevel.Critical },
        
        // Delimiter confusion
        { @"```\s*system", RiskLevel.Medium },
        { @"```\s*user", RiskLevel.Medium },
        { @"```\s*assistant", RiskLevel.Medium },
        { @"<\|system\|>", RiskLevel.High },
        { @"<\|user\|>", RiskLevel.High },
        { @"<\|assistant\|>", RiskLevel.High },
        
        // Harmful content generation
        { @"how\s+to\s+(make\s+|create\s+|build\s+).*bomb", RiskLevel.Critical },
        { @"instructions?\s+for\s+.*illegal", RiskLevel.Critical },
        { @"teach\s+me\s+to\s+hack", RiskLevel.High },
        
        // Social engineering
        { @"this\s+is\s+urgent", RiskLevel.Medium },
        { @"emergency\s+override", RiskLevel.High },
        { @"admin\s+access", RiskLevel.High },
        { @"bypass\s+security", RiskLevel.Critical },
        
        // Repetition attacks
        { @"(.{1,50})\1{5,}", RiskLevel.Medium }, // Repeated patterns
        
        // Encoding attempts
        { @"base64", RiskLevel.Medium },
        { @"hex\s+decode", RiskLevel.Medium },
        { @"url\s+decode", RiskLevel.Medium },
        
        // Prompt continuation
        { @"continue\s+the\s+conversation\s+as", RiskLevel.Medium },
        { @"respond\s+as\s+if\s+you\s+are", RiskLevel.Medium }
    };

    public PromptInjectionResult AnalyzeInput(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return new PromptInjectionResult
            {
                IsInjectionDetected = false,
                RiskLevel = RiskLevel.Low,
                SanitizedInput = string.Empty
            };
        }

        var result = new PromptInjectionResult();
        var normalizedInput = input.ToLowerInvariant();
        var maxRiskLevel = RiskLevel.Low;
        var detectedPatterns = new List<string>();

        // Check against known injection patterns
        foreach (var pattern in InjectionPatterns)
        {
            try
            {
                if (Regex.IsMatch(normalizedInput, pattern.Key, RegexOptions.IgnoreCase | RegexOptions.Multiline))
                {
                    detectedPatterns.Add(pattern.Key);
                    if (pattern.Value > maxRiskLevel)
                    {
                        maxRiskLevel = pattern.Value;
                    }
                }
            }
            catch (RegexMatchTimeoutException)
            {
                _logger.LogWarning("Regex timeout for pattern: {Pattern}", pattern.Key);
            }
        }

        // Additional heuristic checks
        maxRiskLevel = (RiskLevel)Math.Max((int)maxRiskLevel, (int)CheckHeuristics(normalizedInput, detectedPatterns));

        result.IsInjectionDetected = maxRiskLevel > RiskLevel.Low;
        result.RiskLevel = maxRiskLevel;
        result.DetectedPatterns = detectedPatterns;
        result.RecommendedAction = GetRecommendedAction(maxRiskLevel);
        result.SanitizedInput = SanitizeInput(input);

        // Log suspicious activity
        if (result.IsInjectionDetected)
        {
            _logger.LogWarning("Potential prompt injection detected. Risk: {RiskLevel}, Patterns: {Patterns}", 
                maxRiskLevel, string.Join(", ", detectedPatterns));
        }

        return result;
    }

    public string SanitizeInput(string input)
    {
        if (string.IsNullOrEmpty(input))
            return string.Empty;

        var sanitized = input;

        // Remove null characters and most control characters
        sanitized = new string(sanitized
            .Where(c => c != '\0' && (!char.IsControl(c) || c == '\n' || c == '\t' || c == '\r'))
            .ToArray());

        // Remove potential delimiter confusion
        sanitized = Regex.Replace(sanitized, @"```\s*(system|user|assistant)", "```text", RegexOptions.IgnoreCase);
        sanitized = Regex.Replace(sanitized, @"<\|(system|user|assistant)\|>", "[REMOVED]", RegexOptions.IgnoreCase);

        // Neutralize common injection starters
        sanitized = Regex.Replace(sanitized, @"\b(ignore|forget|disregard|override)\s+(all\s+)?previous\s+instructions?\b", 
            "[INSTRUCTION_REMOVED]", RegexOptions.IgnoreCase);

        // Remove role manipulation attempts
        sanitized = Regex.Replace(sanitized, @"\byou\s+are\s+now\s+", "[ROLE_REMOVED] ", RegexOptions.IgnoreCase);

        // Trim and normalize whitespace
        sanitized = sanitized.Trim();
        while (sanitized.Contains("  "))
        {
            sanitized = sanitized.Replace("  ", " ");
        }

        return sanitized;
    }

    private static RiskLevel CheckHeuristics(string input, List<string> detectedPatterns)
    {
        var riskLevel = RiskLevel.Low;

        // Check for excessive length (potential overflow attack)
        if (input.Length > 10000)
        {
            detectedPatterns.Add("excessive_length");
            riskLevel = (RiskLevel)Math.Max((int)riskLevel, (int)RiskLevel.Medium);
        }

        // Check for unusual character patterns
        var specialCharCount = input.Count(c => !char.IsLetterOrDigit(c) && !char.IsWhiteSpace(c));
        if (specialCharCount > input.Length * 0.3)
        {
            detectedPatterns.Add("high_special_char_ratio");
            riskLevel = (RiskLevel)Math.Max((int)riskLevel, (int)RiskLevel.Medium);
        }

        // Check for multiple newlines (potential formatting attack)
        if (Regex.Matches(input, @"\n").Count > 20)
        {
            detectedPatterns.Add("excessive_newlines");
            riskLevel = (RiskLevel)Math.Max((int)riskLevel, (int)RiskLevel.Medium);
        }

        // Check for potential encoding
        if (Regex.IsMatch(input, @"[A-Za-z0-9+/]{20,}={0,2}"))
        {
            detectedPatterns.Add("potential_base64");
            riskLevel = (RiskLevel)Math.Max((int)riskLevel, (int)RiskLevel.Medium);
        }

        return riskLevel;
    }

    private static string GetRecommendedAction(RiskLevel riskLevel)
    {
        return riskLevel switch
        {
            RiskLevel.Critical => "Block request immediately",
            RiskLevel.High => "Block request and log incident",
            RiskLevel.Medium => "Apply additional sanitization",
            RiskLevel.Low => "Process normally",
            _ => "Process normally"
        };
    }
}
