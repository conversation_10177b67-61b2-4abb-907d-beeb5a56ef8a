using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;

namespace AssistantService.Services;

public class AIClient(IHttpClientFactory httpClientFactory, IConfiguration configuration) : IAIClient
{
    private readonly HttpClient _httpClient = httpClientFactory.CreateClient();
    private readonly string? _openAiApiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY");
    private readonly string? _geminiApiKey = Environment.GetEnvironmentVariable("GEMINI_API_KEY");

    public IConfiguration Configuration { get; } = configuration;

    public async Task<string> AskAsync(string prompt, AIProvider provider = AIProvider.OpenAI)
    {
        if (provider == AIProvider.Gemini)
        {
            return await AskGeminiAsync(prompt);
        }
        // Default to OpenAI
        return await AskOpenAIAsync(prompt);
    }

    public async IAsyncEnumerable<string> AskStreamAsync(string prompt, AIProvider provider = AIProvider.OpenAI)
    {
        // For simplicity, just yield the full response (streaming can be implemented later)
        var response = await AskAsync(prompt, provider);
        yield return response;
    }

    private async Task<string> AskOpenAIAsync(string prompt)
    {
        if (string.IsNullOrEmpty(_openAiApiKey))
            return "OpenAI API key not configured.";

        var requestBody = new
        {
            model = "gpt-3.5-turbo",
            messages = new[]
            {
                new { role = "user", content = prompt }
            }
        };

        var request = new HttpRequestMessage(HttpMethod.Post, "https://api.openai.com/v1/chat/completions")
        {
            Content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json")
        };
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _openAiApiKey);

        var response = await _httpClient.SendAsync(request);
        if (!response.IsSuccessStatusCode)
            return $"OpenAI API error: {response.StatusCode}";

        using var stream = await response.Content.ReadAsStreamAsync();
        using var doc = await JsonDocument.ParseAsync(stream);
        var content = doc.RootElement
            .GetProperty("choices")[0]
            .GetProperty("message")
            .GetProperty("content")
            .GetString();

        return content ?? "No response from OpenAI.";
    }

    private async Task<string> AskGeminiAsync(string prompt)
    {
        if (string.IsNullOrEmpty(_geminiApiKey))
            return "Gemini API key not configured.";

        var requestBody = new
        {
            contents = new[]
            {
                new
                {
                    parts = new[]
                    {
                        new { text = prompt }
                    }
                }
            }
        };

        var request = new HttpRequestMessage(HttpMethod.Post, $"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={_geminiApiKey}")
        {
            Content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json")
        };

        var response = await _httpClient.SendAsync(request);
        if (!response.IsSuccessStatusCode)
            return $"Gemini API error: {response.StatusCode}";

        using var stream = await response.Content.ReadAsStreamAsync();
        using var doc = await JsonDocument.ParseAsync(stream);
        var content = doc.RootElement
            .GetProperty("candidates")[0]
            .GetProperty("content")
            .GetProperty("parts")[0]
            .GetProperty("text")
            .GetString();

        return content ?? "No response from Gemini.";
    }

}
