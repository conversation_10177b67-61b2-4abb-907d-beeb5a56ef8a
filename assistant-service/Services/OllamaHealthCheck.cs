using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace AssistantService.Services;

public class OllamaHealthCheck : IHealthCheck
{
    private readonly HttpClient _httpClient;
    private readonly string _ollamaUrl;

    public OllamaHealthCheck(string ollamaUrl)
    {
        _httpClient = new HttpClient();
        _httpClient.Timeout = TimeSpan.FromSeconds(10);
        _ollamaUrl = ollamaUrl;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Simple health check - try to connect to Ollama endpoint
            var response = await _httpClient.GetAsync(_ollamaUrl.Replace("/api/generate", "/api/tags"), cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                return HealthCheckResult.Healthy("Ollama service is accessible");
            }
            else
            {
                return HealthCheckResult.Degraded($"Ollama service returned status: {response.StatusCode}");
            }
        }
        catch (HttpRequestException ex)
        {
            return HealthCheckResult.Unhealthy($"Ollama service is not accessible: {ex.Message}");
        }
        catch (TaskCanceledException)
        {
            return HealthCheckResult.Unhealthy("Ollama service health check timed out");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy($"Ollama health check failed: {ex.Message}");
        }
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}
