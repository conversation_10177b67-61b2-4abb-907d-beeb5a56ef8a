using System.ComponentModel.DataAnnotations;

namespace AssistantService.Models;

public class ChatRequest
{
    [Required(ErrorMessage = "Model is required")]
    public string Model { get; set; } = string.Empty;

    [Required(ErrorMessage = "Messages are required")]
    [MinLength(1, ErrorMessage = "At least one message is required")]
    public List<ChatMessage> Messages { get; set; } = new();

    public bool Stream { get; set; } = true;
}

public class ChatMessage
{
    [Required(ErrorMessage = "Role is required")]
    public string Role { get; set; } = string.Empty;

    [Required(ErrorMessage = "Content is required")]
    [StringLength(4000, ErrorMessage = "Content cannot exceed 4000 characters")]
    public string Content { get; set; } = string.Empty;
}

public class ChatResponse
{
    public ChatMessage Message { get; set; } = new();
    public bool Done { get; set; }
}

public class StreamingChatResponse
{
    public ChatMessage Message { get; set; } = new();
    public bool Done { get; set; } = false;
}
