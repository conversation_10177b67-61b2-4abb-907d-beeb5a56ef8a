using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AssistantService.Models;

/// <summary>
/// Represents a chat message in the AI chat system
/// Maps to the existing ai_chats table in Supabase
/// </summary>
[Table("ai_chats")]
public class AiChatMessage
{
    /// <summary>
    /// Primary key for the chat message
    /// </summary>
    [Key]
    [Column("id")]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// User ID from Supabase auth
    /// </summary>
    [Required]
    [Column("user_id")]
    public Guid UserId { get; set; }

    /// <summary>
    /// Content of the chat message
    /// </summary>
    [Required]
    [Column("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Role of the message sender (user, assistant, system)
    /// </summary>
    [Required]
    [Column("role")]
    [StringLength(20)]
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// Session/conversation identifier to group related messages
    /// </summary>
    [Column("session_id")]
    [StringLength(255)]
    public string? SessionId { get; set; }

    /// <summary>
    /// When the message was created
    /// </summary>
    [Column("created_at")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// AI model used for this message (if added via SQL script)
    /// </summary>
    [Column("model")]
    [StringLength(50)]
    public string? Model { get; set; } = "mistral";

    /// <summary>
    /// When the message was last updated (if added via SQL script)
    /// </summary>
    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Optional metadata for the message (stored as JSON, if added via SQL script)
    /// </summary>
    [Column("metadata", TypeName = "jsonb")]
    public string? MetadataJson { get; set; }

    /// <summary>
    /// Optional title for the conversation (if added via SQL script)
    /// </summary>
    [Column("conversation_title")]
    [StringLength(255)]
    public string? ConversationTitle { get; set; }

    /// <summary>
    /// Order of the message in the conversation (if added via SQL script)
    /// </summary>
    [Column("message_sequence")]
    public int? MessageSequence { get; set; } = 1;

    /// <summary>
    /// Metadata as a dictionary (not mapped to database)
    /// </summary>
    [NotMapped]
    [JsonIgnore]
    public Dictionary<string, object>? Metadata
    {
        get => string.IsNullOrEmpty(MetadataJson)
            ? null
            : JsonSerializer.Deserialize<Dictionary<string, object>>(MetadataJson);
        set => MetadataJson = value == null
            ? null
            : JsonSerializer.Serialize(value);
    }
}

/// <summary>
/// DTO for creating a new chat message
/// </summary>
public class CreateChatMessageRequest
{
    [Required(ErrorMessage = "Message is required")]
    [StringLength(4000, ErrorMessage = "Message cannot exceed 4000 characters")]
    public string Message { get; set; } = string.Empty;

    [Required(ErrorMessage = "Role is required")]
    public string Role { get; set; } = string.Empty;

    public string? SessionId { get; set; }

    public string? Model { get; set; } = "mistral";

    public Dictionary<string, object>? Metadata { get; set; }

    public string? ConversationTitle { get; set; }
}

/// <summary>
/// DTO for chat message response
/// </summary>
public class ChatMessageResponse
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string? SessionId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? Model { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public string? ConversationTitle { get; set; }
    public int? MessageSequence { get; set; }
}

/// <summary>
/// DTO for chat history request with pagination
/// </summary>
public class ChatHistoryRequest
{
    public string? SessionId { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

/// <summary>
/// DTO for chat history response
/// </summary>
public class ChatHistoryResponse
{
    public List<ChatMessageResponse> Messages { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}

/// <summary>
/// DTO for chat sessions list
/// </summary>
public class ChatSessionResponse
{
    public string SessionId { get; set; } = string.Empty;
    public string? ConversationTitle { get; set; }
    public DateTime LastMessageAt { get; set; }
    public int MessageCount { get; set; }
    public string? LastMessage { get; set; }
}
