name: Promote to Production

on:
  workflow_dispatch:
    inputs:
      staging_version:
        description: 'Staging version to promote (e.g., v20240122-abc12345-dev)'
        required: true
        type: string
      run_smoke_tests:
        description: 'Run smoke tests before promotion'
        required: false
        default: true
        type: boolean
      blue_green_deployment:
        description: 'Use blue-green deployment strategy'
        required: false
        default: true
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_PREFIX: ${{ github.repository_owner }}/abraapp

jobs:
  validate-staging:
    name: Validate Staging Version
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Validate version format
      run: |
        VERSION="${{ github.event.inputs.staging_version }}"
        if [[ ! "$VERSION" =~ ^v[0-9]{8}-[a-f0-9]{8}(-dev)?$ ]]; then
          echo "❌ Invalid version format: $VERSION"
          echo "Expected format: v20240122-abc12345-dev"
          exit 1
        fi
        echo "✅ Version format is valid: $VERSION"
    
    - name: Check if images exist in registry
      run: |
        VERSION="${{ github.event.inputs.staging_version }}"
        
        for service in auth-service assistant-service marketdata-service thread-service; do
          IMAGE="${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${service}:${VERSION}"
          echo "Checking if image exists: $IMAGE"
          
          # Use docker manifest inspect to check if image exists
          if docker manifest inspect "$IMAGE" > /dev/null 2>&1; then
            echo "✅ Image exists: $IMAGE"
          else
            echo "❌ Image not found: $IMAGE"
            exit 1
          fi
        done

  smoke-tests:
    name: Run Smoke Tests
    runs-on: ubuntu-latest
    needs: validate-staging
    if: github.event.inputs.run_smoke_tests == 'true'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '10.0.x'
        include-prerelease: true
    
    - name: Run smoke tests against staging
      run: |
        echo "🧪 Running smoke tests against staging environment..."
        
        # Add your smoke test commands here
        # Example: Run a subset of integration tests against staging
        # dotnet test tests/*/Integration/*SmokeTests.cs --configuration Release
        
        echo "✅ Smoke tests passed"

  promote-to-production:
    name: Promote to Production
    runs-on: ubuntu-latest
    needs: [validate-staging, smoke-tests]
    if: always() && (needs.smoke-tests.result == 'success' || needs.smoke-tests.result == 'skipped') && needs.validate-staging.result == 'success'
    environment: production
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Generate production version
      id: prod-version
      run: |
        STAGING_VERSION="${{ github.event.inputs.staging_version }}"
        # Remove -dev suffix and create production version
        PROD_VERSION="${STAGING_VERSION%-dev}"
        echo "production_version=$PROD_VERSION" >> $GITHUB_OUTPUT
        echo "🏷️ Production version: $PROD_VERSION"
    
    - name: Retag images for production
      run: |
        STAGING_VERSION="${{ github.event.inputs.staging_version }}"
        PROD_VERSION="${{ steps.prod-version.outputs.production_version }}"
        
        for service in auth-service assistant-service marketdata-service thread-service; do
          STAGING_IMAGE="${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${service}:${STAGING_VERSION}"
          PROD_IMAGE="${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${service}:${PROD_VERSION}"
          LATEST_IMAGE="${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${service}:latest"
          
          echo "🏷️ Retagging $STAGING_IMAGE -> $PROD_IMAGE"
          
          # Pull staging image
          docker pull "$STAGING_IMAGE"
          
          # Tag for production
          docker tag "$STAGING_IMAGE" "$PROD_IMAGE"
          docker tag "$STAGING_IMAGE" "$LATEST_IMAGE"
          
          # Push production tags
          docker push "$PROD_IMAGE"
          docker push "$LATEST_IMAGE"
          
          echo "✅ Successfully retagged $service"
        done
    
    - name: Load environment variables from GitHub secrets
      run: |
        # Export secrets as environment variables for subsequent steps
        echo "POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}" >> $GITHUB_ENV
        echo "POSTGRES_USER=${{ secrets.POSTGRES_USER }}" >> $GITHUB_ENV
        echo "POSTGRES_DB=${{ secrets.POSTGRES_DB }}" >> $GITHUB_ENV
        echo "SUPABASE_JWT_SECRET=${{ secrets.SUPABASE_JWT_SECRET }}" >> $GITHUB_ENV
        echo "SUPABASE_URL=${{ secrets.SUPABASE_URL }}" >> $GITHUB_ENV
        echo "SUPABASE_PROJECT_ID=${{ secrets.SUPABASE_PROJECT_ID }}" >> $GITHUB_ENV
        echo "SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" >> $GITHUB_ENV
        echo "SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> $GITHUB_ENV
        echo "SUPABASE_CONNECTION_STRING=${{ secrets.SUPABASE_CONNECTION_STRING }}" >> $GITHUB_ENV
        echo "FINNHUB_API_KEY=${{ secrets.FINNHUB_API_KEY }}" >> $GITHUB_ENV
        echo "POLYGON_API_KEY=${{ secrets.POLYGON_API_KEY }}" >> $GITHUB_ENV
        echo "REDIS_CONNECTION_STRING=${{ secrets.REDIS_CONNECTION_STRING }}" >> $GITHUB_ENV
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> $GITHUB_ENV
        echo "GEMINI_API_KEY=${{ secrets.GEMINI_API_KEY }}" >> $GITHUB_ENV

    - name: Create Kubernetes secrets from .env
      run: |
        PROD_VERSION="${{ steps.prod-version.outputs.production_version }}"

        # Create secrets directory
        mkdir -p k8s-secrets

        # Generate auth-service secret
        cat > k8s-secrets/auth-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: auth-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_PROJECT_ID: $(echo -n "$SUPABASE_PROJECT_ID" | base64 -w 0)
          SUPABASE_SERVICE_ROLE_KEY: $(echo -n "$SUPABASE_SERVICE_ROLE_KEY" | base64 -w 0)
          SUPABASE_ANON_KEY: $(echo -n "$SUPABASE_ANON_KEY" | base64 -w 0)
        EOF

        # Generate marketdata-service secret
        cat > k8s-secrets/marketdata-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: marketdata-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_CONNECTION_STRING: $(echo -n "$SUPABASE_CONNECTION_STRING" | base64 -w 0)
          FINNHUB_API_KEY: $(echo -n "$FINNHUB_API_KEY" | base64 -w 0)
          POLYGON_API_KEY: $(echo -n "$POLYGON_API_KEY" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_PROJECT_ID: $(echo -n "$SUPABASE_PROJECT_ID" | base64 -w 0)
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
          SUPABASE_ANON_KEY: $(echo -n "$SUPABASE_ANON_KEY" | base64 -w 0)
          SUPABASE_SERVICE_ROLE_KEY: $(echo -n "$SUPABASE_SERVICE_ROLE_KEY" | base64 -w 0)
        EOF

        # Generate thread-service secret
        cat > k8s-secrets/thread-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: thread-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_CONNECTION_STRING: $(echo -n "$SUPABASE_CONNECTION_STRING" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_PROJECT_ID: $(echo -n "$SUPABASE_PROJECT_ID" | base64 -w 0)
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
          SUPABASE_ANON_KEY: $(echo -n "$SUPABASE_ANON_KEY" | base64 -w 0)
          SUPABASE_SERVICE_ROLE_KEY: $(echo -n "$SUPABASE_SERVICE_ROLE_KEY" | base64 -w 0)
        EOF

        # Generate assistant-service secret
        cat > k8s-secrets/assistant-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: assistant-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_CONNECTION_STRING: $(echo -n "$SUPABASE_CONNECTION_STRING" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_PROJECT_ID: $(echo -n "$SUPABASE_PROJECT_ID" | base64 -w 0)
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
          SUPABASE_ANON_KEY: $(echo -n "$SUPABASE_ANON_KEY" | base64 -w 0)
          SUPABASE_SERVICE_ROLE_KEY: $(echo -n "$SUPABASE_SERVICE_ROLE_KEY" | base64 -w 0)
          OPENAI_API_KEY: $(echo -n "$OPENAI_API_KEY" | base64 -w 0)
          GEMINI_API_KEY: $(echo -n "$GEMINI_API_KEY" | base64 -w 0)
        EOF

    - name: Update Kubernetes manifests for production
      run: |
        PROD_VERSION="${{ steps.prod-version.outputs.production_version }}"

        # Update image tags in deployment files
        for service in auth-service assistant-service marketdata-service thread-service; do
          sed -i "s|image: ghcr.io/abraapp/${service}:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${service}:${PROD_VERSION}|g" ${service}/k8s/deployment.yaml
        done

        # Update Redis connection string in marketdata-service config
        sed -i "s|Redis__ConnectionString:.*|Redis__ConnectionString: \"$REDIS_CONNECTION_STRING\"|g" marketdata-service/k8s/config.yaml

        echo "📝 Updated Kubernetes manifests with production version: $PROD_VERSION"
    
    - name: Deploy to Production (Blue-Green)
      if: github.event.inputs.blue_green_deployment == 'true'
      run: |
        echo "🔵 Starting blue-green deployment to production..."
        PROD_VERSION="${{ steps.prod-version.outputs.production_version }}"
        
        # Deploy to green environment first
        echo "🟢 Deploying to green environment..."
        
        # Update green upstream configuration
        cp nginx/upstreams.green.conf nginx/upstreams.conf
        
        # Setup kubectl for K3s
        mkdir -p $HOME/.kube
        echo "${{ secrets.KUBE_CONFIG_K3S }}" | base64 -d > $HOME/.kube/config
        chmod 600 $HOME/.kube/config

        # Apply infrastructure first
        kubectl apply -f infrastructure/k8s/namespaces/namespace.yml

        # Apply infrastructure nginx ingress
        kubectl apply -f infrastructure/k8s/nginx/ingress.yml

        # Apply other infrastructure manifests
        kubectl apply -f infrastructure/k8s/cert-manager/
        kubectl apply -f infrastructure/k8s/postgres/
        kubectl apply -f infrastructure/k8s/redis/

        # Apply generated secrets
        kubectl apply -f k8s-secrets/

        # Apply services
        kubectl apply -f auth-service/k8s/
        kubectl apply -f assistant-service/k8s/
        kubectl apply -f marketdata-service/k8s/
        kubectl apply -f thread-service/k8s/
        
        echo "⏳ Waiting for green environment to be ready..."
        # Add health check logic here
        
        echo "🔄 Switching traffic to green environment..."
        # Switch nginx upstream to green
        # kubectl exec -n abraapi deployment/nginx -- nginx -s reload
        
        echo "✅ Blue-green deployment completed successfully"
    
    - name: Deploy to Production (Rolling Update)
      if: github.event.inputs.blue_green_deployment == 'false'
      run: |
        echo "🔄 Starting rolling update deployment to production..."
        PROD_VERSION="${{ steps.prod-version.outputs.production_version }}"
        
        # Setup kubectl for K3s
        mkdir -p $HOME/.kube
        echo "${{ secrets.KUBE_CONFIG_K3S }}" | base64 -d > $HOME/.kube/config
        chmod 600 $HOME/.kube/config

        # Apply infrastructure first
        kubectl apply -f infrastructure/k8s/

        # Apply generated secrets
        kubectl apply -f k8s-secrets/

        # Apply services with rolling update
        kubectl apply -f auth-service/k8s/
        kubectl apply -f assistant-service/k8s/
        kubectl apply -f marketdata-service/k8s/
        kubectl apply -f thread-service/k8s/
        
        # Wait for rollout to complete
        # kubectl rollout status deployment/auth-service -n abraapi --context=production
        # kubectl rollout status deployment/assistant-service -n abraapi --context=production
        # kubectl rollout status deployment/marketdata-service -n abraapi --context=production
        # kubectl rollout status deployment/thread-service -n abraapi --context=production
        
        echo "✅ Rolling update deployment completed successfully"
    
    - name: Post-deployment verification
      run: |
        echo "🔍 Running post-deployment verification..."
        
        # Add your production health checks here
        # curl -f https://abraapp.undeclab.com/health
        # curl -f https://abraapp.undeclab.com/api/auth/health
        # curl -f https://abraapp.undeclab.com/api/marketdata/health
        # curl -f https://abraapp.undeclab.com/api/assistant/health
        
        echo "✅ Production deployment verified successfully"
        echo "🎉 Version ${{ steps.prod-version.outputs.production_version }} is now live in production!"

  rollback:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    needs: promote-to-production
    if: failure()
    environment: production
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup kubectl for K3s rollback
      run: |
        mkdir -p $HOME/.kube
        echo "${{ secrets.KUBE_CONFIG_K3S }}" | base64 -d > $HOME/.kube/config
        chmod 600 $HOME/.kube/config

    - name: Rollback production deployment
      run: |
        echo "🚨 Deployment failed, initiating rollback..."

        # Rollback all services to previous version
        kubectl rollout undo deployment/auth-service -n abraapi
        kubectl rollout undo deployment/assistant-service -n abraapi
        kubectl rollout undo deployment/marketdata-service -n abraapi
        kubectl rollout undo deployment/thread-service -n abraapi

        # Wait for rollback to complete
        kubectl rollout status deployment/auth-service -n abraapi --timeout=300s
        kubectl rollout status deployment/assistant-service -n abraapi --timeout=300s
        kubectl rollout status deployment/marketdata-service -n abraapi --timeout=300s
        kubectl rollout status deployment/thread-service -n abraapi --timeout=300s

        echo "🔄 Rollback completed successfully"
