<Solution>
  <Folder Name="/Solution Items/">
    <File Path="README.md" />
    <File Path="docker-compose.yml" />
    <File Path="deploy.sh" />
  </Folder>
  
  <Folder Name="/Services/">
    <Project Path="auth-service/AuthService.csproj" Type="C#" />
    <Project Path="assistant-service/AssistantService.csproj" Type="C#" />
    <Project Path="marketdata-service/MarketDataService.csproj" Type="C#" />
    <Project Path="thread-service/ThreadService.csproj" Type="C#" />
  </Folder>
  
  <Folder Name="/Tests/">
    <Project Path="tests/auth-service.Tests/auth-service.Tests.csproj" Type="C#" />
    <Project Path="tests/assistant-service.Tests/assistant-service.Tests.csproj" Type="C#" />
    <Project Path="tests/marketdata-service.Tests/marketdata-service.Tests.csproj" Type="C#" />
  </Folder>
  
  <Folder Name="/Infrastructure/">
    <Folder Name="/Kubernetes/">
      <File Path="infrastructure/k8s/namespace.yaml" />
      <File Path="infrastructure/k8s/configmap.yaml" />
      <File Path="infrastructure/k8s/secrets.yaml" />
    </Folder>
    <Folder Name="/Service Configs/">
      <File Path="auth-service/k8s/deployment.yaml" />
      <File Path="auth-service/k8s/service.yaml" />
      <File Path="auth-service/k8s/ingress.yaml" />
      <File Path="assistant-service/k8s/deployment.yaml" />
      <File Path="assistant-service/k8s/service.yaml" />
      <File Path="marketdata-service/k8s/deployment.yaml" />
      <File Path="marketdata-service/k8s/service.yaml" />
      <File Path="thread-service/k8s/deployment.yaml" />
      <File Path="thread-service/k8s/service.yaml" />
    </Folder>
    <Folder Name="/Nginx/">
      <File Path="nginx/nginx.conf" />
      <File Path="nginx/nginx.blue-green.conf" />
      <File Path="nginx/upstreams.blue.conf" />
      <File Path="nginx/upstreams.green.conf" />
    </Folder>
  </Folder>
  
  <Folder Name="/Scripts/">
    <File Path="k3s/services/install-k3s.sh" />
    <File Path="k3s/services/deploy-services.sh" />
  </Folder>
</Solution>
