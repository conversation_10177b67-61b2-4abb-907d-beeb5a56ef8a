#!/bin/bash

# Deploy all Kubernetes resources for abraapp microservices
# Usage: ./deploy.sh

set -e

echo "🚀 Deploying abraapp microservices to Kubernetes..."

# Deploy infrastructure first
echo "📦 Deploying infrastructure..."
kubectl apply -f infrastructure/k8s/

# Deploy services
echo "🔐 Deploying auth-service..."
kubectl apply -f auth-service/k8s/

echo "📊 Deploying marketdata-service..."
kubectl apply -f marketdata-service/k8s/

echo "🤖 Deploying assistant-service..."
kubectl apply -f assistant-service/k8s/

echo "💬 Deploying thread-service..."
kubectl apply -f thread-service/k8s/

echo "✅ All services deployed successfully!"
echo "🔍 Check deployment status with: kubectl get pods -n abraapi"
