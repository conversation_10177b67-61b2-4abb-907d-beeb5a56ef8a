apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: abraapi-ingress
  namespace: abraapi
  annotations:
    kubernetes.io/ingress.class: "traefik"  # K3s uses Traefik by default
spec:
  tls:
  - hosts:
    - abraapp.undeclab.com
    secretName: abraapi-tls
  rules:
  - host: abraapp.undeclab.com
    http:
      paths:
      # Assistant Service routes
      - path: /api/assistant
        pathType: Prefix
        backend:
          service:
            name: assistant-service
            port:
              number: 8080

      # Auth Service routes
      - path: /api/auth
        pathType: Prefix
        backend:
          service:
            name: auth-service
            port:
              number: 8080

      # Market Data Service routes
      - path: /api/marketdata
        pathType: Prefix
        backend:
          service:
            name: marketdata-service
            port:
              number: 8080

      # Thread Service routes
      - path: /api/threads
        pathType: Prefix
        backend:
          service:
            name: thread-service
            port:
              number: 8080

      # Health check endpoint 
      - path: /health
        pathType: Exact
        backend:
          service:
            name: auth-service
            port: 
              number: 8080
