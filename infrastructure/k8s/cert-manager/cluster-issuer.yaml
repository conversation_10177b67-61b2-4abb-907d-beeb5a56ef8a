apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    # Use Let’s Encrypt production endpoint; for testing swap to -staging
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>     # for let'sEncrypt notifications
    privateKeySecretRef:
      name: letsencrypt-prod-secret
    solvers:
      - http01:
          ingress:
            class: traefik
