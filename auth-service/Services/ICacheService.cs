namespace AuthService.Services;

/// <summary>
/// Interface for caching service to improve performance by caching frequently accessed data
/// </summary>
public interface ICacheService
{
    /// <summary>
    /// Cache user profile data to reduce API calls to Supabase
    /// </summary>
    Task<T?> GetAsync<T>(string key) where T : class;
    
    /// <summary>
    /// Set cache entry with expiration
    /// </summary>
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
    
    /// <summary>
    /// Remove cache entry
    /// </summary>
    Task RemoveAsync(string key);
    
    /// <summary>
    /// Check if cache contains key
    /// </summary>
    Task<bool> ExistsAsync(string key);
    
    /// <summary>
    /// Get or set cache entry with factory method
    /// </summary>
    Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan? expiration = null) where T : class;
}
