using Microsoft.Extensions.Caching.Memory;
using System.Text.Json;

namespace AuthService.Services;

/// <summary>
/// Memory cache implementation for caching frequently accessed data
/// </summary>
public class MemoryCacheService : ICacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<MemoryCacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public MemoryCacheService(IMemoryCache memoryCache, ILogger<MemoryCacheService> logger)
    {
        _memoryCache = memoryCache;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public Task<T?> GetAsync<T>(string key) where T : class
    {
        try
        {
            if (_memoryCache.TryGetValue(key, out var cachedValue))
            {
                _logger.LogDebug("Cache hit for key: {Key}", key);
                return Task.FromResult(cachedValue as T);
            }

            _logger.LogDebug("Cache miss for key: {Key}", key);
            return Task.FromResult<T?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache entry for key: {Key}", key);
            return Task.FromResult<T?>(null);
        }
    }

    public Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
    {
        try
        {
            var options = new MemoryCacheEntryOptions();
            
            if (expiration.HasValue)
            {
                options.AbsoluteExpirationRelativeToNow = expiration.Value;
            }
            else
            {
                // Default expiration times based on data type
                options.AbsoluteExpirationRelativeToNow = GetDefaultExpiration<T>();
            }

            // Set sliding expiration to keep frequently accessed items longer
            options.SlidingExpiration = TimeSpan.FromMinutes(5);
            
            // Set priority to help with memory management
            options.Priority = CacheItemPriority.Normal;

            _memoryCache.Set(key, value, options);
            _logger.LogDebug("Cache entry set for key: {Key}, expires in: {Expiration}", key, options.AbsoluteExpirationRelativeToNow);
            
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache entry for key: {Key}", key);
            return Task.CompletedTask;
        }
    }

    public Task RemoveAsync(string key)
    {
        try
        {
            _memoryCache.Remove(key);
            _logger.LogDebug("Cache entry removed for key: {Key}", key);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache entry for key: {Key}", key);
            return Task.CompletedTask;
        }
    }

    public Task<bool> ExistsAsync(string key)
    {
        try
        {
            return Task.FromResult(_memoryCache.TryGetValue(key, out _));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
            return Task.FromResult(false);
        }
    }

    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan? expiration = null) where T : class
    {
        try
        {
            // Try to get from cache first
            var cachedValue = await GetAsync<T>(key);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            // Not in cache, use factory to get value
            _logger.LogDebug("Cache miss for key: {Key}, using factory method", key);
            var value = await factory();
            
            if (value != null)
            {
                await SetAsync(key, value, expiration);
            }

            return value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetOrSetAsync for key: {Key}", key);
            // Fallback to factory method if cache fails
            try
            {
                return await factory();
            }
            catch (Exception factoryEx)
            {
                _logger.LogError(factoryEx, "Factory method also failed for key: {Key}", key);
                return null;
            }
        }
    }

    /// <summary>
    /// Get default expiration time based on data type
    /// </summary>
    private static TimeSpan GetDefaultExpiration<T>()
    {
        var typeName = typeof(T).Name;
        
        return typeName switch
        {
            // User profiles can be cached longer as they don't change frequently
            "SupabaseUser" or "UserProfile" => TimeSpan.FromMinutes(30),
            
            // Auth responses should be cached for shorter periods
            "AuthResponse" => TimeSpan.FromMinutes(10),
            
            // JWT validation results can be cached briefly
            "TokenValidationResult" => TimeSpan.FromMinutes(5),
            
            // Default expiration
            _ => TimeSpan.FromMinutes(15)
        };
    }
}
