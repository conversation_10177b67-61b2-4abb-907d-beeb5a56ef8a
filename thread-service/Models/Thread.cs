using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ThreadService.Models
{
    public class Thread
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid UserId { get; set; }

        [Required]
        public string Content { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<Comment> Comments { get; set; } = new List<Comment>();
        public virtual ICollection<Like> Likes { get; set; } = new List<Like>();
        public virtual ICollection<Share> Shares { get; set; } = new List<Share>();

        // Computed properties
        [NotMapped]
        public int LikeCount => Likes?.Count ?? 0;

        [NotMapped]
        public int ShareCount => Shares?.Count ?? 0;

        [NotMapped]
        public int CommentCount => Comments?.Count ?? 0;
    }
}
