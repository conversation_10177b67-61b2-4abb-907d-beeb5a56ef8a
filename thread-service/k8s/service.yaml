apiVersion: v1
kind: Service
metadata:
  name: thread-service
  namespace: abraapi
  labels:
    app: thread-service
    service: thread
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: thread-service
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: thread-service-hpa
  namespace: abraapi
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: thread-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
