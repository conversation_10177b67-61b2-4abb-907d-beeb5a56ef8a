using System.ComponentModel.DataAnnotations;

namespace ThreadService.DTOs
{
    public class CreateCommentRequest
    {
        [Required]
        public string ThreadId { get; set; } = string.Empty;

        [Required]
        public string Content { get; set; } = string.Empty;
    }

    public class UpdateCommentRequest
    {
        [Required]
        public string Content { get; set; } = string.Empty;
    }

    public class CommentResponse
    {
        public string Id { get; set; } = string.Empty;
        public string ThreadId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CommentFeedResponse
    {
        public List<CommentResponse> Comments { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage { get; set; }
    }

    public class CommentFeedRequest
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? ThreadId { get; set; }
        public string? UserId { get; set; }
        public CommentSortBy SortBy { get; set; } = CommentSortBy.CreatedAt;
        public SortDirection SortDirection { get; set; } = SortDirection.Descending;
    }

    public enum CommentSortBy
    {
        CreatedAt,
        UpdatedAt
    }

    public enum SortDirection
    {
        Ascending,
        Descending
    }
}
