using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ThreadService.DTOs;
using ThreadService.Services;

namespace ThreadService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class LikesController(ILikeShareService likeShareService) : ControllerBase
    {
        private readonly ILikeShareService _likeShareService = likeShareService;

        private string GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                ?? User.FindFirst("sub")?.Value
                ?? throw new UnauthorizedAccessException("User ID not found in token");
        }

        [HttpPost("toggle")]
        public async Task<ActionResult<LikeResponse>> ToggleLike([FromBody] LikeRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _likeShareService.ToggleLikeAsync(userId, request.ThreadId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("share/toggle")]
        public async Task<ActionResult<ShareResponse>> ToggleShare([FromBody] ShareRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _likeShareService.ToggleShareAsync(userId, request.ThreadId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
