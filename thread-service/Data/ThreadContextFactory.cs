using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using DotNetEnv;

namespace ThreadService.Data
{
    public class ThreadContextFactory : IDesignTimeDbContextFactory<ThreadContext>
    {
        public ThreadContext CreateDbContext(string[] args)
        {
            // Try to load .env from multiple locations
            var envPaths = new[]
            {
                "../.env",           // Root level (development)
                ".env",              // Current directory (development)
                "../../.env",        // Parent directory (development)
                "/app/.env",         // Container root (production)
                "/src/.env"          // Alternative container path
            };

            foreach (var envPath in envPaths)
            {
                if (File.Exists(envPath))
                {
                    DotNetEnv.Env.Load(envPath);
                    Console.WriteLine($"Loaded .env from: {envPath}");
                    break;
                }
            }

            // Get connection string from environment variable
            var connectionString = Environment.GetEnvironmentVariable("SUPABASE_CONNECTION_STRING");
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException(
                    "SUPABASE_CONNECTION_STRING environment variable is not set. " +
                    "Make sure you have a .env file in the root directory or set the environment variable directly."
                );
            }

            var optionsBuilder = new DbContextOptionsBuilder<ThreadContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new ThreadContext(optionsBuilder.Options);
        }
    }
}
