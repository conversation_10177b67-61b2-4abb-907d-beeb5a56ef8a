using System.Text.Json;
using System.Text.Json.Serialization;
using MarketDataService.Configuration;
using MarketDataService.Interfaces;
using MarketDataService.Models;
using Microsoft.Extensions.Options;

namespace MarketDataService.Brokers;

public class FinnhubProvider(HttpClient http, IOptions<BrokerApiKeys> keys) : IPriceProvider, IHistoricalPriceProvider, IBrokerMetadataProvider, ISymbolProvider
{
    public string BrokerId => "finnhub";
    private readonly HttpClient _http = http;
    private readonly string _apiKey = keys.Value.Finnhub;

    public async Task<decimal?> FetchPriceAsync(string symbol)
    {
        var res = await _http.GetAsync($"https://finnhub.io/api/v1/quote?symbol={symbol}&token={_apiKey}");
        if (!res.IsSuccessStatusCode) return null;

        var json = await res.Content.ReadAsStringAsync();
        try
        {
            var parsed = JsonSerializer.Deserialize<FinnhubQuoteResponse>(json);
            return parsed?.C;
        }
        catch (JsonException)
        {
            return null;
        }
    }

    public async Task<List<HistoricalPrice>?> GetHistoricalPricesAsync(string symbol, DateTime from, DateTime to, string interval)
    {
        string resolution = interval switch
        {
            "1d" => "D",
            "1h" => "60",
            _ => "D"
        };

        long fromUnix = ((DateTimeOffset)from).ToUnixTimeSeconds();
        long toUnix = ((DateTimeOffset)to).ToUnixTimeSeconds();

        var url = $"https://finnhub.io/api/v1/stock/candle?symbol={symbol}&resolution={resolution}&from={fromUnix}&to={toUnix}&token={_apiKey}";
        var res = await _http.GetAsync(url);
        if (!res.IsSuccessStatusCode) return null;

        var json = await res.Content.ReadAsStringAsync();
        var parsed = JsonSerializer.Deserialize<FinnhubHistoryResponse>(json);

        if (parsed == null || parsed.S != "ok") return null;

        var list = new List<HistoricalPrice>();
        for (int i = 0; i < parsed.T.Length; i++)
        {
            list.Add(new HistoricalPrice
            {
                Date = DateTimeOffset.FromUnixTimeSeconds(parsed.T[i]).UtcDateTime,
                Open = parsed.O[i],
                High = parsed.H[i],
                Low = parsed.L[i],
                Close = parsed.C[i],
                Volume = parsed.V[i]
            });
        }
        return list;
    }

    public BrokerMetadata GetMetadata() => new()
    {
        Id = BrokerId,
        Name = "Finnhub",
        Status = "online",
        SupportedAssets = ["stocks", "crypto", "forex"],
        DocsUrl = "https://finnhub.io/docs/api"
    };

    // ISymbolProvider implementation
    public async Task<List<Symbol>> GetAllSymbolsAsync()
    {
        var symbols = new List<Symbol>();

        // Get stocks symbols
        var stockSymbols = await GetSymbolsByMarketTypeAsync("stocks");
        symbols.AddRange(stockSymbols);

        // Get forex symbols
        var forexSymbols = await GetSymbolsByMarketTypeAsync("forex");
        symbols.AddRange(forexSymbols);

        // Get crypto symbols
        var cryptoSymbols = await GetSymbolsByMarketTypeAsync("crypto");
        symbols.AddRange(cryptoSymbols);

        return symbols;
    }

    public async Task<List<Symbol>> GetSymbolsByMarketTypeAsync(string marketType)
    {
        return marketType.ToLower() switch
        {
            "stocks" => await GetStockSymbolsAsync(),
            "forex" => await GetForexSymbolsAsync(),
            "crypto" => await GetCryptoSymbolsAsync(),
            _ => []
        };
    }

    public Task<List<ActiveSymbol>> GetMostActiveSymbolsAsync(string? marketType = null, int limit = 50)
    {
        // For stocks, we can use market news or trending symbols
        // For now, return a basic implementation with popular symbols
        var activeSymbols = new List<ActiveSymbol>();

        if (marketType == null || marketType.ToLower() == "stocks")
        {
            var popularStocks = new[] { "AAPL", "GOOGL", "MSFT", "AMZN", "TSLA", "META", "NVDA", "NFLX", "AMD", "INTC" };
            for (int i = 0; i < Math.Min(popularStocks.Length, limit); i++)
            {
                activeSymbols.Add(new ActiveSymbol
                {
                    Code = popularStocks[i],
                    Name = GetStockName(popularStocks[i]),
                    MarketType = "stocks",
                    Exchange = "NASDAQ",
                    Currency = "USD",
                    Country = "US",
                    BrokerId = BrokerId,
                    IsActive = true,
                    Rank = i + 1,
                    LastUpdated = DateTime.UtcNow
                });
            }
        }

        return Task.FromResult(activeSymbols.Take(limit).ToList());
    }

    public async Task<List<SymbolSearchResult>> SearchSymbolsAsync(string query, string? marketType = null, int limit = 20)
    {
        try
        {
            var url = $"https://finnhub.io/api/v1/search?q={Uri.EscapeDataString(query)}&token={_apiKey}";
            var res = await _http.GetAsync(url);
            if (!res.IsSuccessStatusCode) return [];

            var json = await res.Content.ReadAsStringAsync();
            var parsed = JsonSerializer.Deserialize<FinnhubSearchResponse>(json);

            if (parsed?.Result == null) return [];

            var results = new List<SymbolSearchResult>();
            foreach (var item in parsed.Result.Take(limit))
            {
                if (marketType != null && !IsMatchingMarketType(item.Type, marketType))
                    continue;

                results.Add(new SymbolSearchResult
                {
                    Code = item.Symbol,
                    Name = item.Description,
                    MarketType = GetMarketTypeFromFinnhubType(item.Type),
                    Exchange = "",
                    Currency = "USD",
                    Country = "US",
                    BrokerId = BrokerId,
                    IsActive = true,
                    RelevanceScore = CalculateRelevanceScore(query, item.Symbol, item.Description),
                    MatchType = GetMatchType(query, item.Symbol, item.Description),
                    Description = item.Description,
                    LastUpdated = DateTime.UtcNow
                });
            }

            return [.. results.OrderByDescending(r => r.RelevanceScore)];
        }
        catch (Exception)
        {
            return [];
        }
    }

    // Helper methods for symbol fetching
    private async Task<List<Symbol>> GetStockSymbolsAsync()
    {
        try
        {
            var url = $"https://finnhub.io/api/v1/stock/symbol?exchange=US&token={_apiKey}";
            var res = await _http.GetAsync(url);
            if (!res.IsSuccessStatusCode) return [];

            var json = await res.Content.ReadAsStringAsync();
            var parsed = JsonSerializer.Deserialize<FinnhubStockSymbol[]>(json);

            if (parsed == null) return [];

            return [.. parsed.Select(s => new Symbol
            {
                Code = s.Symbol,
                Name = s.Description,
                MarketType = "stocks",
                Exchange = "US",
                Currency = s.Currency ?? "USD",
                Country = "US",
                BrokerId = BrokerId,
                IsActive = true,
                LastUpdated = DateTime.UtcNow
            })];
        }
        catch (Exception)
        {
            return [];
        }
    }

    private async Task<List<Symbol>> GetForexSymbolsAsync()
    {
        try
        {
            var url = $"https://finnhub.io/api/v1/forex/symbol?exchange=oanda&token={_apiKey}";
            var res = await _http.GetAsync(url);
            if (!res.IsSuccessStatusCode) return [];

            var json = await res.Content.ReadAsStringAsync();
            var parsed = JsonSerializer.Deserialize<FinnhubForexSymbol[]>(json);

            if (parsed == null) return [];

            return [.. parsed.Select(s => new Symbol
            {
                Code = s.Symbol,
                Name = s.Description,
                MarketType = "forex",
                Exchange = "OANDA",
                Currency = "USD",
                Country = "US",
                BrokerId = BrokerId,
                IsActive = true,
                LastUpdated = DateTime.UtcNow
            })];
        }
        catch (Exception)
        {
            return [];
        }
    }

    private async Task<List<Symbol>> GetCryptoSymbolsAsync()
    {
        try
        {
            var url = $"https://finnhub.io/api/v1/crypto/symbol?exchange=binance&token={_apiKey}";
            var res = await _http.GetAsync(url);
            if (!res.IsSuccessStatusCode) return [];

            var json = await res.Content.ReadAsStringAsync();
            var parsed = JsonSerializer.Deserialize<FinnhubCryptoSymbol[]>(json);

            if (parsed == null) return [];

            return [.. parsed.Select(s => new Symbol
            {
                Code = s.Symbol,
                Name = s.Description,
                MarketType = "crypto",
                Exchange = "BINANCE",
                Currency = "USD",
                Country = "US",
                BrokerId = BrokerId,
                IsActive = true,
                LastUpdated = DateTime.UtcNow
            })];
        }
        catch (Exception)
        {
            return [];
        }
    }

    private static string GetStockName(string symbol)
    {
        return symbol switch
        {
            "AAPL" => "Apple Inc.",
            "GOOGL" => "Alphabet Inc.",
            "MSFT" => "Microsoft Corporation",
            "AMZN" => "Amazon.com Inc.",
            "TSLA" => "Tesla Inc.",
            "META" => "Meta Platforms Inc.",
            "NVDA" => "NVIDIA Corporation",
            "NFLX" => "Netflix Inc.",
            "AMD" => "Advanced Micro Devices Inc.",
            "INTC" => "Intel Corporation",
            _ => symbol
        };
    }

    private static bool IsMatchingMarketType(string finnhubType, string requestedType)
    {
        var marketType = GetMarketTypeFromFinnhubType(finnhubType);
        return marketType.Equals(requestedType, StringComparison.OrdinalIgnoreCase);
    }

    private static string GetMarketTypeFromFinnhubType(string finnhubType)
    {
        return finnhubType?.ToLower() switch
        {
            "common stock" => "stocks",
            "stock" => "stocks",
            "forex" => "forex",
            "crypto" => "crypto",
            _ => "stocks"
        };
    }

    private static double CalculateRelevanceScore(string query, string symbol, string description)
    {
        var score = 0.0;
        var queryLower = query.ToLower();
        var symbolLower = symbol.ToLower();
        var descriptionLower = description.ToLower();

        // Exact symbol match gets highest score
        if (symbolLower == queryLower) score += 100;
        else if (symbolLower.StartsWith(queryLower)) score += 80;
        else if (symbolLower.Contains(queryLower)) score += 60;

        // Description matches
        if (descriptionLower.Contains(queryLower)) score += 40;

        return score;
    }

    private static string GetMatchType(string query, string symbol, string description)
    {
        var queryLower = query.ToLower();
        if (symbol.Contains(queryLower, StringComparison.CurrentCultureIgnoreCase)) return "symbol";
        if (description.Contains(queryLower, StringComparison.CurrentCultureIgnoreCase)) return "name";
        return "description";
    }

    // Record types for API responses
    private record FinnhubQuoteResponse([property: JsonPropertyName("c")] decimal C);
    private record FinnhubHistoryResponse(
        [property: JsonPropertyName("o")] decimal[] O,
        [property: JsonPropertyName("h")] decimal[] H,
        [property: JsonPropertyName("l")] decimal[] L,
        [property: JsonPropertyName("c")] decimal[] C,
        [property: JsonPropertyName("t")] long[] T,
        [property: JsonPropertyName("v")] long[] V,
        [property: JsonPropertyName("s")] string S);
    private record FinnhubSearchResponse([property: JsonPropertyName("result")] FinnhubSearchResult[] Result);
    private record FinnhubSearchResult(
        [property: JsonPropertyName("symbol")] string Symbol,
        [property: JsonPropertyName("description")] string Description,
        [property: JsonPropertyName("type")] string Type);
    private record FinnhubStockSymbol(
        [property: JsonPropertyName("symbol")] string Symbol,
        [property: JsonPropertyName("description")] string Description,
        [property: JsonPropertyName("currency")] string? Currency);
    private record FinnhubForexSymbol(
        [property: JsonPropertyName("symbol")] string Symbol,
        [property: JsonPropertyName("description")] string Description);
    private record FinnhubCryptoSymbol(
        [property: JsonPropertyName("symbol")] string Symbol,
        [property: JsonPropertyName("description")] string Description);
}