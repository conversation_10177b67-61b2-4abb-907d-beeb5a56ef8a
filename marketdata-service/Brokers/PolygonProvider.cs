using System.Text.Json;
using MarketDataService.Configuration;
using MarketDataService.Interfaces;
using MarketDataService.Models;
using Microsoft.Extensions.Options;

namespace MarketDataService.Brokers;

public class PolygonProvider(HttpClient http, IOptions<BrokerApiKeys> keys) : IPriceProvider, IHistoricalPriceProvider, IBrokerMetadataProvider, ISymbolProvider
{
    public string BrokerId => "polygon";
    private readonly HttpClient _http = http;
    private readonly string _apiKey = keys.Value.Polygon;

    public async Task<decimal?> FetchPriceAsync(string symbol)
    {
        var url = $"https://api.polygon.io/v2/last/trade/stocks/{symbol}?apiKey={_apiKey}";
        var res = await _http.GetAsync(url);
        if (!res.IsSuccessStatusCode) return null;

        var json = await res.Content.ReadAsStringAsync();
        var parsed = JsonSerializer.Deserialize<PolygonQuoteResponse>(json);
        return parsed?.Last?.Price;
    }

    public async Task<List<HistoricalPrice>?> GetHistoricalPricesAsync(string symbol, DateTime from, DateTime to, string interval)
    {
        var fromStr = from.ToString("yyyy-MM-dd");
        var toStr = to.ToString("yyyy-MM-dd");
        var url = $"https://api.polygon.io/v2/aggs/ticker/{symbol}/range/1/day/{fromStr}/{toStr}?adjusted=true&sort=asc&apiKey={_apiKey}";

        var res = await _http.GetAsync(url);
        if (!res.IsSuccessStatusCode) return null;

        var json = await res.Content.ReadAsStringAsync();
        var parsed = JsonSerializer.Deserialize<PolygonHistoryResponse>(json);
        if (parsed?.Results == null) return null;

        return parsed.Results.Select(r => new HistoricalPrice
        {
            Date = DateTimeOffset.FromUnixTimeMilliseconds(r.t).UtcDateTime,
            Open = r.o,
            High = r.h,
            Low = r.l,
            Close = r.c,
            Volume = r.v
        }).ToList();
    }

    public BrokerMetadata GetMetadata() => new()
    {
        Id = BrokerId,
        Name = "Polygon.io",
        Status = "online",
        SupportedAssets = new() { "stocks", "crypto" },
        DocsUrl = "https://polygon.io/docs"
    };

    // ISymbolProvider implementation
    public async Task<List<Symbol>> GetAllSymbolsAsync()
    {
        var symbols = new List<Symbol>();

        // Get stocks symbols
        var stockSymbols = await GetSymbolsByMarketTypeAsync("stocks");
        symbols.AddRange(stockSymbols);

        // Get crypto symbols
        var cryptoSymbols = await GetSymbolsByMarketTypeAsync("crypto");
        symbols.AddRange(cryptoSymbols);

        return symbols;
    }

    public async Task<List<Symbol>> GetSymbolsByMarketTypeAsync(string marketType)
    {
        return marketType.ToLower() switch
        {
            "stocks" => await GetStockSymbolsAsync(),
            "crypto" => await GetCryptoSymbolsAsync(),
            _ => []
        };
    }

    public Task<List<ActiveSymbol>> GetMostActiveSymbolsAsync(string? marketType = null, int limit = 50)
    {
        // For stocks, return popular symbols with mock data
        var activeSymbols = new List<ActiveSymbol>();

        if (marketType == null || marketType.Equals("stocks", StringComparison.OrdinalIgnoreCase))
        {
            var popularStocks = new[] { "AAPL", "GOOGL", "MSFT", "AMZN", "TSLA", "META", "NVDA", "NFLX", "AMD", "INTC" };
            for (int i = 0; i < Math.Min(popularStocks.Length, limit); i++)
            {
                activeSymbols.Add(new ActiveSymbol
                {
                    Code = popularStocks[i],
                    Name = GetStockName(popularStocks[i]),
                    MarketType = "stocks",
                    Exchange = "NASDAQ",
                    Currency = "USD",
                    Country = "US",
                    BrokerId = BrokerId,
                    IsActive = true,
                    Rank = i + 1,
                    LastUpdated = DateTime.UtcNow
                });
            }
        }

        return Task.FromResult(activeSymbols.Take(limit).ToList());
    }

    public async Task<List<SymbolSearchResult>> SearchSymbolsAsync(string query, string? marketType = null, int limit = 20)
    {
        try
        {
            // Polygon doesn't have a direct symbol search API, so we'll use reference data
            var url = $"https://api.polygon.io/v3/reference/tickers?search={Uri.EscapeDataString(query)}&limit={limit}&apikey={_apiKey}";
            var res = await _http.GetAsync(url);
            if (!res.IsSuccessStatusCode) return [];

            var json = await res.Content.ReadAsStringAsync();
            var parsed = JsonSerializer.Deserialize<PolygonTickersResponse>(json);

            if (parsed?.results == null) return [];

            var results = new List<SymbolSearchResult>();
            foreach (var ticker in parsed.results)
            {
                if (marketType != null && !IsMatchingMarketType(ticker.market, marketType))
                    continue;

                results.Add(new SymbolSearchResult
                {
                    Code = ticker.ticker,
                    Name = ticker.name,
                    MarketType = GetMarketTypeFromPolygonMarket(ticker.market),
                    Exchange = ticker.primary_exchange ?? "",
                    Currency = ticker.currency_name ?? "USD",
                    Country = ticker.locale ?? "US",
                    BrokerId = BrokerId,
                    IsActive = ticker.active,
                    RelevanceScore = CalculateRelevanceScore(query, ticker.ticker, ticker.name),
                    MatchType = GetMatchType(query, ticker.ticker, ticker.name),
                    Description = ticker.name,
                    LastUpdated = DateTime.UtcNow
                });
            }

            return results.OrderByDescending(r => r.RelevanceScore).ToList();
        }
        catch (Exception)
        {
            return [];
        }
    }

    // Helper methods for symbol fetching
    private async Task<List<Symbol>> GetStockSymbolsAsync()
    {
        try
        {
            var url = $"https://api.polygon.io/v3/reference/tickers?market=stocks&active=true&limit=1000&apikey={_apiKey}";
            var res = await _http.GetAsync(url);
            if (!res.IsSuccessStatusCode) return [];

            var json = await res.Content.ReadAsStringAsync();
            var parsed = JsonSerializer.Deserialize<PolygonTickersResponse>(json);

            if (parsed?.results == null) return [];

            return parsed.results.Select(t => new Symbol
            {
                Code = t.ticker,
                Name = t.name,
                MarketType = "stocks",
                Exchange = t.primary_exchange ?? "NASDAQ",
                Currency = t.currency_name ?? "USD",
                Country = t.locale ?? "US",
                BrokerId = BrokerId,
                IsActive = t.active,
                LastUpdated = DateTime.UtcNow
            }).ToList();
        }
        catch (Exception)
        {
            return [];
        }
    }

    private async Task<List<Symbol>> GetCryptoSymbolsAsync()
    {
        try
        {
            var url = $"https://api.polygon.io/v3/reference/tickers?market=crypto&active=true&limit=1000&apikey={_apiKey}";
            var res = await _http.GetAsync(url);
            if (!res.IsSuccessStatusCode) return [];

            var json = await res.Content.ReadAsStringAsync();
            var parsed = JsonSerializer.Deserialize<PolygonTickersResponse>(json);

            if (parsed?.results == null) return [];

            return parsed.results.Select(t => new Symbol
            {
                Code = t.ticker,
                Name = t.name,
                MarketType = "crypto",
                Exchange = "CRYPTO",
                Currency = t.currency_name ?? "USD",
                Country = t.locale ?? "US",
                BrokerId = BrokerId,
                IsActive = t.active,
                LastUpdated = DateTime.UtcNow
            }).ToList();
        }
        catch (Exception)
        {
            return [];
        }
    }

    private static string GetStockName(string symbol)
    {
        return symbol switch
        {
            "AAPL" => "Apple Inc.",
            "GOOGL" => "Alphabet Inc.",
            "MSFT" => "Microsoft Corporation",
            "AMZN" => "Amazon.com Inc.",
            "TSLA" => "Tesla Inc.",
            "META" => "Meta Platforms Inc.",
            "NVDA" => "NVIDIA Corporation",
            "NFLX" => "Netflix Inc.",
            "AMD" => "Advanced Micro Devices Inc.",
            "INTC" => "Intel Corporation",
            _ => symbol
        };
    }

    private static bool IsMatchingMarketType(string polygonMarket, string requestedType)
    {
        var marketType = GetMarketTypeFromPolygonMarket(polygonMarket);
        return marketType.Equals(requestedType, StringComparison.OrdinalIgnoreCase);
    }

    private static string GetMarketTypeFromPolygonMarket(string polygonMarket)
    {
        return polygonMarket?.ToLower() switch
        {
            "stocks" => "stocks",
            "crypto" => "crypto",
            "fx" => "forex",
            _ => "stocks"
        };
    }

    private static double CalculateRelevanceScore(string query, string symbol, string description)
    {
        var score = 0.0;
        var queryLower = query.ToLower();
        var symbolLower = symbol.ToLower();
        var descriptionLower = description.ToLower();

        // Exact symbol match gets highest score
        if (symbolLower == queryLower) score += 100;
        else if (symbolLower.StartsWith(queryLower)) score += 80;
        else if (symbolLower.Contains(queryLower)) score += 60;

        // Description matches
        if (descriptionLower.Contains(queryLower)) score += 40;

        return score;
    }

    private static string GetMatchType(string query, string symbol, string description)
    {
        var queryLower = query.ToLower();
        if (symbol.ToLower().Contains(queryLower)) return "symbol";
        if (description.ToLower().Contains(queryLower)) return "name";
        return "description";
    }

    // Record types for API responses
    private record PolygonQuoteResponse(LastTrade Last);
    private record LastTrade(decimal p) { public decimal Price => p; };
    private record PolygonHistoryResponse(List<PolygonAgg> Results);
    private record PolygonAgg(long t, decimal o, decimal h, decimal l, decimal c, long v);
    private record PolygonTickersResponse(PolygonTicker[] results);
    private record PolygonTicker(
        string ticker,
        string name,
        string market,
        string? locale,
        string? primary_exchange,
        string? currency_name,
        bool active
    );
}