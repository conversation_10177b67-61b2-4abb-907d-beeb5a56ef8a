using MarketDataService.Interfaces;
using MarketDataService.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MarketDataService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SymbolController(ISymbolRegistry symbolRegistry, ILogger<SymbolController> logger) : ControllerBase
{
    private readonly ISymbolRegistry _symbolRegistry = symbolRegistry;
    private readonly ILogger<SymbolController> _logger = logger;

    /// <summary>
    /// Get all available symbols from all brokers or a specific broker
    /// </summary>
    [HttpGet]
    [ResponseCache(Duration = 3600, VaryByQueryKeys = new[] { "brokerId" })]
    public async Task<ActionResult<SymbolResponse<Symbol>>> GetAllSymbols([FromQuery] string? brokerId = null)
    {
        try
        {
            _logger.LogInformation("Fetching all symbols for broker: {BrokerId}", brokerId ?? "all");
            var result = await _symbolRegistry.GetAllSymbolsAsync(brokerId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching all symbols");
            return StatusCode(500, "Failed to fetch symbols");
        }
    }

    /// <summary>
    /// Get symbols filtered by market type (stocks, forex, crypto)
    /// </summary>
    [HttpGet("market/{marketType}")]
    [ResponseCache(Duration = 3600, VaryByQueryKeys = new[] { "marketType", "brokerId" })]
    public async Task<ActionResult<SymbolResponse<Symbol>>> GetSymbolsByMarketType(
        [FromRoute] string marketType,
        [FromQuery] string? brokerId = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(marketType))
                return BadRequest("Market type is required");

            var validMarketTypes = new[] { "stocks", "forex", "crypto" };
            if (!validMarketTypes.Contains(marketType.ToLower()))
                return BadRequest($"Invalid market type. Valid types: {string.Join(", ", validMarketTypes)}");

            _logger.LogInformation("Fetching {MarketType} symbols for broker: {BrokerId}", marketType, brokerId ?? "all");
            var result = await _symbolRegistry.GetSymbolsByMarketTypeAsync(marketType, brokerId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching symbols for market type: {MarketType}", marketType);
            return StatusCode(500, "Failed to fetch symbols");
        }
    }

    /// <summary>
    /// Get the most active/traded symbols
    /// </summary>
    [HttpGet("active")]
    [ResponseCache(Duration = 300, VaryByQueryKeys = new[] { "marketType", "brokerId", "limit" })]
    public async Task<ActionResult<SymbolResponse<ActiveSymbol>>> GetMostActiveSymbols(
        [FromQuery] string? marketType = null,
        [FromQuery] string? brokerId = null,
        [FromQuery] int limit = 50)
    {
        try
        {
            if (limit <= 0 || limit > 100)
                return BadRequest("Limit must be between 1 and 100");

            if (marketType != null)
            {
                var validMarketTypes = new[] { "stocks", "forex", "crypto" };
                if (!validMarketTypes.Contains(marketType.ToLower()))
                    return BadRequest($"Invalid market type. Valid types: {string.Join(", ", validMarketTypes)}");
            }

            _logger.LogInformation("Fetching most active symbols - MarketType: {MarketType}, Broker: {BrokerId}, Limit: {Limit}", 
                marketType ?? "all", brokerId ?? "all", limit);
            var result = await _symbolRegistry.GetMostActiveSymbolsAsync(marketType, brokerId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching most active symbols");
            return StatusCode(500, "Failed to fetch active symbols");
        }
    }

    /// <summary>
    /// Search for symbols by query string
    /// </summary>
    [HttpGet("search")]
    [ResponseCache(Duration = 600, VaryByQueryKeys = new[] { "q", "marketType", "brokerId", "limit" })]
    public async Task<ActionResult<SymbolResponse<SymbolSearchResult>>> SearchSymbols(
        [FromQuery] string q,
        [FromQuery] string? marketType = null,
        [FromQuery] string? brokerId = null,
        [FromQuery] int limit = 20)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(q))
                return BadRequest("Search query is required");

            if (q.Length < 1)
                return BadRequest("Search query must be at least 1 character long");

            if (limit <= 0 || limit > 100)
                return BadRequest("Limit must be between 1 and 100");

            if (marketType != null)
            {
                var validMarketTypes = new[] { "stocks", "forex", "crypto" };
                if (!validMarketTypes.Contains(marketType.ToLower()))
                    return BadRequest($"Invalid market type. Valid types: {string.Join(", ", validMarketTypes)}");
            }

            _logger.LogInformation("Searching symbols - Query: {Query}, MarketType: {MarketType}, Broker: {BrokerId}, Limit: {Limit}", 
                q, marketType ?? "all", brokerId ?? "all", limit);
            var result = await _symbolRegistry.SearchSymbolsAsync(q, marketType, brokerId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching symbols with query: {Query}", q);
            return StatusCode(500, "Failed to search symbols");
        }
    }

    /// <summary>
    /// Get available brokers that support symbol fetching
    /// </summary>
    [HttpGet("brokers")]
    [ResponseCache(Duration = 3600)]
    public ActionResult<IEnumerable<string>> GetAvailableBrokers()
    {
        try
        {
            var brokers = _symbolRegistry.GetAvailableBrokerIds();
            return Ok(brokers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching available brokers");
            return StatusCode(500, "Failed to fetch available brokers");
        }
    }

    /// <summary>
    /// Bulk symbol search with request body
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<SymbolResponse<SymbolSearchResult>>> BulkSearchSymbols([FromBody] SymbolSearchRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Query))
                return BadRequest("Search query is required");

            if (request.Limit <= 0 || request.Limit > 100)
                return BadRequest("Limit must be between 1 and 100");

            if (request.MarketType != null)
            {
                var validMarketTypes = new[] { "stocks", "forex", "crypto" };
                if (!validMarketTypes.Contains(request.MarketType.ToLower()))
                    return BadRequest($"Invalid market type. Valid types: {string.Join(", ", validMarketTypes)}");
            }

            _logger.LogInformation("Bulk searching symbols - Query: {Query}, MarketType: {MarketType}, Broker: {BrokerId}, Limit: {Limit}", 
                request.Query, request.MarketType ?? "all", request.BrokerId ?? "all", request.Limit);
            var result = await _symbolRegistry.SearchSymbolsAsync(request.Query, request.MarketType, request.BrokerId, request.Limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in bulk symbol search");
            return StatusCode(500, "Failed to search symbols");
        }
    }

    /// <summary>
    /// Get most active symbols with request body
    /// </summary>
    [HttpPost("active")]
    public async Task<ActionResult<SymbolResponse<ActiveSymbol>>> BulkGetMostActiveSymbols([FromBody] MostActiveSymbolsRequest request)
    {
        try
        {
            if (request.Limit <= 0 || request.Limit > 100)
                return BadRequest("Limit must be between 1 and 100");

            if (request.MarketType != null)
            {
                var validMarketTypes = new[] { "stocks", "forex", "crypto" };
                if (!validMarketTypes.Contains(request.MarketType.ToLower()))
                    return BadRequest($"Invalid market type. Valid types: {string.Join(", ", validMarketTypes)}");
            }

            _logger.LogInformation("Bulk fetching most active symbols - MarketType: {MarketType}, Broker: {BrokerId}, Limit: {Limit}", 
                request.MarketType ?? "all", request.BrokerId ?? "all", request.Limit);
            var result = await _symbolRegistry.GetMostActiveSymbolsAsync(request.MarketType, request.BrokerId, request.Limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in bulk fetching most active symbols");
            return StatusCode(500, "Failed to fetch active symbols");
        }
    }
}
