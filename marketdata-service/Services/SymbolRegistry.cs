using System.Diagnostics;
using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Services;

public class SymbolRegistry(IEnumerable<ISymbolProvider> providers, ILogger<SymbolRegistry> logger) : ISymbolRegistry
{
    private readonly List<ISymbolProvider> _providers = providers.ToList();
    private readonly ILogger<SymbolRegistry> _logger = logger;

    public IEnumerable<ISymbolProvider> GetAllProviders() => _providers;

    public ISymbolProvider? GetProvider(string brokerId) =>
        _providers.FirstOrDefault(p => p.BrokerId == brokerId);

    public IEnumerable<string> GetAvailableBrokerIds() =>
        _providers.Select(p => p.BrokerId);

    public async Task<SymbolResponse<Symbol>> GetAllSymbolsAsync(string? brokerId = null)
    {
        var stopwatch = Stopwatch.StartNew();
        var allSymbols = new List<Symbol>();
        var providers = GetTargetProviders(brokerId);

        foreach (var provider in providers)
        {
            try
            {
                _logger.LogDebug("Fetching all symbols from broker: {BrokerId}", provider.BrokerId);
                var symbols = await provider.GetAllSymbolsAsync();
                allSymbols.AddRange(symbols);
                _logger.LogDebug("Retrieved {Count} symbols from broker: {BrokerId}", symbols.Count, provider.BrokerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching symbols from broker: {BrokerId}", provider.BrokerId);
            }
        }

        stopwatch.Stop();
        return new SymbolResponse<Symbol>
        {
            Symbols = allSymbols,
            TotalCount = allSymbols.Count,
            BrokerId = brokerId ?? "all",
            ProcessingTime = stopwatch.Elapsed
        };
    }

    public async Task<SymbolResponse<Symbol>> GetSymbolsByMarketTypeAsync(string marketType, string? brokerId = null)
    {
        var stopwatch = Stopwatch.StartNew();
        var allSymbols = new List<Symbol>();
        var providers = GetTargetProviders(brokerId);

        foreach (var provider in providers)
        {
            try
            {
                _logger.LogDebug("Fetching {MarketType} symbols from broker: {BrokerId}", marketType, provider.BrokerId);
                var symbols = await provider.GetSymbolsByMarketTypeAsync(marketType);
                allSymbols.AddRange(symbols);
                _logger.LogDebug("Retrieved {Count} {MarketType} symbols from broker: {BrokerId}", symbols.Count, marketType, provider.BrokerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching {MarketType} symbols from broker: {BrokerId}", marketType, provider.BrokerId);
            }
        }

        stopwatch.Stop();
        return new SymbolResponse<Symbol>
        {
            Symbols = allSymbols,
            TotalCount = allSymbols.Count,
            BrokerId = brokerId ?? "all",
            ProcessingTime = stopwatch.Elapsed
        };
    }

    public async Task<SymbolResponse<ActiveSymbol>> GetMostActiveSymbolsAsync(string? marketType = null, string? brokerId = null, int limit = 50)
    {
        var stopwatch = Stopwatch.StartNew();
        var allActiveSymbols = new List<ActiveSymbol>();
        var providers = GetTargetProviders(brokerId);

        foreach (var provider in providers)
        {
            try
            {
                _logger.LogDebug("Fetching most active symbols from broker: {BrokerId}, MarketType: {MarketType}, Limit: {Limit}", 
                    provider.BrokerId, marketType ?? "all", limit);
                var symbols = await provider.GetMostActiveSymbolsAsync(marketType, limit);
                allActiveSymbols.AddRange(symbols);
                _logger.LogDebug("Retrieved {Count} active symbols from broker: {BrokerId}", symbols.Count, provider.BrokerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching active symbols from broker: {BrokerId}", provider.BrokerId);
            }
        }

        // Sort by rank and take the requested limit
        var sortedSymbols = allActiveSymbols
            .OrderBy(s => s.Rank)
            .ThenByDescending(s => s.Volume ?? 0)
            .Take(limit)
            .ToList();

        stopwatch.Stop();
        return new SymbolResponse<ActiveSymbol>
        {
            Symbols = sortedSymbols,
            TotalCount = sortedSymbols.Count,
            BrokerId = brokerId ?? "all",
            ProcessingTime = stopwatch.Elapsed
        };
    }

    public async Task<SymbolResponse<SymbolSearchResult>> SearchSymbolsAsync(string query, string? marketType = null, string? brokerId = null, int limit = 20)
    {
        var stopwatch = Stopwatch.StartNew();
        var allResults = new List<SymbolSearchResult>();
        var providers = GetTargetProviders(brokerId);

        foreach (var provider in providers)
        {
            try
            {
                _logger.LogDebug("Searching symbols in broker: {BrokerId}, Query: {Query}, MarketType: {MarketType}, Limit: {Limit}", 
                    provider.BrokerId, query, marketType ?? "all", limit);
                var results = await provider.SearchSymbolsAsync(query, marketType, limit);
                allResults.AddRange(results);
                _logger.LogDebug("Found {Count} matching symbols in broker: {BrokerId}", results.Count, provider.BrokerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching symbols in broker: {BrokerId}", provider.BrokerId);
            }
        }

        // Sort by relevance score and take the requested limit
        var sortedResults = allResults
            .OrderByDescending(r => r.RelevanceScore)
            .ThenBy(r => r.Code)
            .Take(limit)
            .ToList();

        stopwatch.Stop();
        return new SymbolResponse<SymbolSearchResult>
        {
            Symbols = sortedResults,
            TotalCount = sortedResults.Count,
            BrokerId = brokerId ?? "all",
            ProcessingTime = stopwatch.Elapsed
        };
    }

    private IEnumerable<ISymbolProvider> GetTargetProviders(string? brokerId)
    {
        if (string.IsNullOrWhiteSpace(brokerId))
        {
            return _providers;
        }

        var provider = GetProvider(brokerId);
        return provider != null ? [provider] : [];
    }
}
