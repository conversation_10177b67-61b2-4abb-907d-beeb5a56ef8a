using MarketDataService.Interfaces;

namespace MarketDataService.Services;

public class HistoricalPriceRegistry(IEnumerable<IHistoricalPriceProvider> providers) : IHistoricalPriceRegistry
{
    private readonly Dictionary<string, IHistoricalPriceProvider> _providers = providers.ToDictionary(p => p.BrokerId.ToLower());

    public IHistoricalPriceProvider? GetProvider(string brokerId)
    {
        _providers.TryGetValue(brokerId.ToLower(), out var provider);
        return provider;
    }
}