namespace MarketDataService.Models;

public class Watchlist
{
    public long Id { get; set; }
    public Guid? UserId { get; set; }
    public string Name { get; set; } = default!;
    public DateTime? CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; } = DateTime.UtcNow;
    public bool? IsGlobal { get; set; } = false;

    public List<WatchlistItem> Items { get; set; } = new();
}

public class WatchlistItem
{
    public long Id { get; set; }
    public long? WatchlistId { get; set; }
    public string Symbol { get; set; } = default!;
    public string? Broker { get; set; }
    public DateTime? AddedAt { get; set; } = DateTime.UtcNow;
    public Guid? UserId { get; set; }
    public int SortOrder { get; set; } = 0;

    public Watchlist? Watchlist { get; set; }
}
