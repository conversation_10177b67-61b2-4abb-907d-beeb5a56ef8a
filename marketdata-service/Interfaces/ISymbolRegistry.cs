using MarketDataService.Models;

namespace MarketDataService.Interfaces;

public interface ISymbolRegistry
{
    /// <summary>
    /// Gets all available symbol providers
    /// </summary>
    IEnumerable<ISymbolProvider> GetAllProviders();

    /// <summary>
    /// Gets a specific symbol provider by broker ID
    /// </summary>
    ISymbolProvider? GetProvider(string brokerId);

    /// <summary>
    /// Gets all symbols from all brokers
    /// </summary>
    Task<SymbolResponse<Symbol>> GetAllSymbolsAsync(string? brokerId = null);

    /// <summary>
    /// Gets symbols filtered by market type from all brokers or specific broker
    /// </summary>
    Task<SymbolResponse<Symbol>> GetSymbolsByMarketTypeAsync(string marketType, string? brokerId = null);

    /// <summary>
    /// Gets the most active symbols from all brokers or specific broker
    /// </summary>
    Task<SymbolResponse<ActiveSymbol>> GetMostActiveSymbolsAsync(string? marketType = null, string? brokerId = null, int limit = 50);

    /// <summary>
    /// Searches for symbols across all brokers or specific broker
    /// </summary>
    Task<SymbolResponse<SymbolSearchResult>> SearchSymbolsAsync(string query, string? marketType = null, string? brokerId = null, int limit = 20);

    /// <summary>
    /// Gets available broker IDs that support symbol fetching
    /// </summary>
    IEnumerable<string> GetAvailableBrokerIds();
}
