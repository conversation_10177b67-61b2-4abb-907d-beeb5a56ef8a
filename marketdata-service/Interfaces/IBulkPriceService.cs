using MarketDataService.Models;

namespace MarketDataService.Interfaces;

public interface IBulkPriceService
{
    /// <summary>
    /// Fetches prices for multiple symbols in parallel with caching
    /// </summary>
    Task<BulkPriceResponse> GetBulkPricesAsync(List<string> symbols, string? brokerId = null, bool bypassCache = false);

    /// <summary>
    /// Gets symbol prices with calculated changes and percentages
    /// </summary>
    Task<List<SymbolPriceDto>> GetSymbolPricesWithHistoryAsync(List<string> symbols, string? brokerId = null);

    /// <summary>
    /// Gets watchlist with all symbols and their current prices in a single call
    /// </summary>
    Task<WatchlistWithPricesDto> GetWatchlistWithPricesAsync(long watchlistId, Guid userId);

    /// <summary>
    /// Calculates price analytics for multiple symbols (changes, percentages, etc.)
    /// </summary>
    Task<List<PriceAnalyticsDto>> GetPriceAnalyticsAsync(List<string> symbols, string? brokerId = null);
}
