using MarketDataService.Models;

namespace MarketDataService.Interfaces;

public interface IWatchlistService
{
    Task<bool> TestDatabaseConnectionAsync();
    Task<List<WatchlistDto>> GetUserWatchlistsAsync(Guid userId);
    Task<WatchlistDto?> GetWatchlistAsync(Guid userId, long watchlistId, bool includePrices = false);
    Task<WatchlistDto> CreateWatchlistAsync(Guid userId, CreateWatchlistRequest request);
    Task<WatchlistDto?> UpdateWatchlistAsync(Guid userId, long watchlistId, UpdateWatchlistRequest request);
    Task<bool> DeleteWatchlistAsync(Guid userId, long watchlistId);
    Task<WatchlistDto?> AddSymbolsAsync(Guid userId, long watchlistId, AddSymbolsRequest request);
    Task<bool> RemoveSymbolAsync(Guid userId, long watchlistId, string symbol);
    Task<WatchlistDto?> ReorderItemsAsync(Guid userId, long watchlistId, ReorderItemsRequest request);
    Task<WatchlistDto?> GetGlobalWatchlistAsync(Guid userId, bool includePrices = false);
}
